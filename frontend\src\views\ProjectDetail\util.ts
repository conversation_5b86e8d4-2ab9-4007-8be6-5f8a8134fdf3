import html2canvas from 'html2canvas';
import jspdf from 'jspdf';
import { baseConfig } from '@/config/baseConfig'

export const outputInfo2 = () => {
  return {
    baseInfo: {
      mainTitle: '基本信息',
      viceTitle: { label: '光伏装机容量', name: '', unit: 'MW' },
      list: [
        { label: '光伏装机容量', name: 'pv_capacity', unit: 'MW' },
        { label: '风机装机容量', name: 'wind_capacity', unit: 'MW' },
        { label: '储能装机容量', name: 'es_capacity', unit: 'MW' },
        { label: '电解槽装机容量', name: 'ele_capacity', unit: 'MW' },
        { label: '光储LCOE', name: 'lcoe', unit: '元/kwh' },
        { label: 'LCOH', name: 'lcoh', unit: '元/Nm³' },
      ]
    },
    eleStat: {
      mainTitle: '电量统计',
      viceTitle:  { label: '年下网电量', name: '', unit: '' },
      list: [
        { label: '绿电年制氢用电', name: '', unit: 'Kwh' },
        { label: '储能年制氢用电', name: 'es_quantity', unit: 'Kwh' },
        { label: '下网电量', name: 'grid_down_quantity', unit: 'Kwh' },
        { label: '下网比例', name: 'grid_down_radio', unit: 'Kwh' },
        { label: '上网电量', name: 'grid_up_quantity', unit: 'Kwh' },
        { label: '上网比例', name: 'grid_up_radio', unit: '' },
        { label: '光伏年发电量', name: '', unit: 'Kwh' },
        { label: '风电年发电量', name: '', unit: 'Kwh' },
        { label: '绿电年总发电量', name: 'gen_quantity', unit: 'Kwh' },
      ]
    },
    h2CreateStat: {
      mainTitle: '产氢统计',
      viceTitle:  { label: '装机容量', name: '', unit: 'MW' },
      list: [
        { label: '绿电制氢量', name: 'h2_ge_quantity', unit: '万Nm³/y' },
        { label: '储能制氢量', name: 'h2_es_quantity', unit: '万Nm³/y' },
        { label: '电网制氢量', name: 'h2_grid_quantity', unit: '万Nm³/y' },
        { label: '系统年总制氢量', name: 'h2_quantity', unit: '万Nm³/y' },
        { label: '绿电弃电率', name: 'abort_radio', unit: '' },
        { label: '弃氢量', name: 'abort_h2_quantity', unit: 'Nm³' },
      ]
    },
    hourStat: {
      mainTitle: '小时数统计',
      viceTitle:  { label: '', name: '', unit: '' },
      list: [
        { label: '绿电制氢小时数', name: 'h2_ge_hours', unit: 'h' },
        { label: '光伏发电小时数', name: 'pv_gen_hours', unit: 'h' },
        { label: '风电发电小时数', name: 'wind_gen_hours', unit: 'h' },
        { label: '储能制氢小时数', name: 'h2_es_hours', unit: 'h' },
        { label: '电网制氢小时数', name: 'h2_grid_hours', unit: 'h' },
        { label: '总制氢小时数', name: 'h2_hours', unit: 'h' },
      ]
    },
    h2StoreStat: {
      mainTitle: '储氢统计',
      viceTitle:  {},
      list: [
        { label: '储罐个数', name: ' ', unit: '' },
        { label: '储罐容量', name: 'hs_capacity', unit: '' },
        { label: '储罐占地面积', name: 'h2_area', unit: '' },
      ]
    },
  }
}

export const outputInfo = () => {
  return [
    {
      pic: baseConfig.p1,
      mainTitle: '基本信息',
      viceTitle: { label: '光伏装机容量', name: '', unit: 'MW' },
      list: [
        { label: '光伏装机容量', name: 'pv_capacity', unit: 'MW', editable: true },
        { label: '风机装机容量', name: 'wind_capacity', unit: 'MW', editable: true },
        { label: '储能装机容量', name: 'es_capacity', unit: 'MWh', editable: true },
        { label: '电解槽装机容量', name: 'ele_capacity', unit: 'MW', editable: true },
        { label: '光储LCOE', name: 'lcoe', unit: '元/kwh' },
        { label: 'LCOH', name: 'lcoh', unit: '元/Nm³' },
      ]
    },
    {
      pic: baseConfig.p2,
      mainTitle: '电量统计',
      viceTitle:  { label: '年下网电量', name: '', unit: '' },
      list: [
        { label: '绿电年制氢用电', name: 'ge_quantity', unit: 'Mwh' },
        { label: '储能年制氢用电', name: 'es_quantity', unit: 'Mwh' },
        { label: '下网电量', name: 'grid_down_quantity', unit: 'Mwh'},
        { label: '下网比例', name: 'grid_down_radio', unit: '%', editable: true, numberType: 'ratio' },
        { label: '上网电量', name: 'grid_up_quantity', unit: 'Mwh' },
        { label: '上网比例', name: 'grid_up_radio', unit: '%', editable: true, numberType: 'ratio' },
        { label: '光伏年发电量', name: 'pv_gen_quantity', unit: 'Mwh' },
        { label: '风电年发电量', name: 'wind_gen_quantity', unit: 'Mwh' },
        { label: '绿电年总发电量', name: 'gen_quantity', unit: 'Mwh' },
      ]
    },
    {
      pic: baseConfig.p3,
      mainTitle: '产氢统计',
      viceTitle:  { label: '装机容量', name: '', unit: 'MW' },
      list: [
        { label: '绿电制氢量', name: 'h2_ge_quantity', unit: '吨/y' },
        { label: '储能制氢量', name: 'h2_es_quantity', unit: '吨/y' },
        { label: '电网制氢量', name: 'h2_grid_quantity', unit: '吨/y' },
        { label: '年总制氢量', name: 'h2_quantity', unit: '吨/y' },
        { label: '绿电弃电率', name: 'abort_radio', unit: '%', numberType: 'ratio' },
        { label: '弃氢量', name: 'abort_h2_quantity', unit: 'Nm³' },
      ]
    },
    {
      pic: baseConfig.p4,
      mainTitle: '小时数统计',
      viceTitle:  { label: '', name: '', unit: '' },
      list: [
        { label: '绿电制氢小时数', name: 'h2_ge_hours', unit: 'h' },
        { label: '光伏发电小时数', name: 'pv_gen_hours', unit: 'h' },
        { label: '风电发电小时数', name: 'wind_gen_hours', unit: 'h' },
        { label: '储能制氢小时数', name: 'h2_es_hours', unit: 'h' },
        { label: '电网制氢小时数', name: 'h2_grid_hours', unit: 'h' },
        { label: '总制氢小时数', name: 'h2_hours', unit: 'h' },
      ]
    },
    {
      pic: baseConfig.p5,
      mainTitle: '电解槽产品',
      viceTitle:  {},
      key: 'alk'
    },
    {
      pic: baseConfig.p6,
      key: 'alkStorage',
      mainTitle: '储氢统计',
      viceTitle:  {},
      list: [
        { label: '储罐个数', name: 'alkStoreNum', unit: '', editable: true },
        { label: '储罐容量(m³)', name: 'hs_capacity', unit: '' },
        // { label: '储罐占地面积', name: 'h2_area', unit: '' },
      ]
    },
  ]
}

export async function downloadPdf(elementId, name = '报告') {
  // elementId 为需要下载的元素的 id
  const element = document.getElementById(elementId);
  if (element) {
    html2canvas(element, {
      useCORS: true, // 允许加载跨域图片
      onclone: (clone) => {
        // clone 为克隆出来的整个页面的 DOM 节点
        if (elementId) {
          const cloneDOM = clone.getElementById(elementId);
    
          cloneDOM.querySelector(`#${elementId} .tmp_title`).style.display = 'flex'
          cloneDOM.querySelector(`#${elementId} .tmp_tail`).style.display = 'flex'
          // 将需要下载的元素的宽度调整为 A4 纸的宽度
          // 注意：这里的操作不会修改原 DOM，因此会和页面渲染的不一致，如果需要保持和页面一致则不需要此操作
          // cloneDOM.style.width = `592.28px`; // TODO
          cloneDOM.style.width = `${cloneDOM.offsetWidth}px`; // TODO
        }
      },
    }).then((canvas) => {
      // element.removeChild(titleDom)

      // canvas 渲染的是需要下载的元素
      const pdf = new jspdf("p", "pt", [592.28, 841.89]); // 创建一个 pdf
      const ctx = canvas.getContext("2d");
      const margin = 30; // pdf 每页的边距
      const a4w = 592.28 - 2 * margin,
        a4h = 841.89 - 2 * margin; // pdf 内容的宽高
      const imgHeight = Math.floor((a4h * canvas.width) / a4w); // 将 canvas 按 pdf 内容的高度分页，imgHeight 为分页的高度
      let renderedHeight = 0; // 已经添加到 pdf 的高度

      while (renderedHeight < canvas.height) {
        // canvas 没有全部添加到 pdf
        const page = document.createElement("canvas"); // 创建一个新的画布
        // 将 canvas 切割后渲染到新的画布上
        page.width = canvas.width;
        page.height = Math.min(imgHeight, canvas.height - renderedHeight);
        page
          .getContext("2d")
          .putImageData(
            ctx.getImageData(
              0,
              renderedHeight,
              canvas.width,
              Math.min(imgHeight, canvas.height - renderedHeight)
            ),
            0,
            0
          );
        // 将新画布生成图片并添加到 pdf 中
        pdf.addImage(
          page.toDataURL("image/jpeg", 1.0),
          "JPEG",
          margin,
          margin,
          a4w,
          Math.min(a4h, (a4w * page.height) / page.width)
        );
        renderedHeight += imgHeight;
        if (renderedHeight < canvas.height)
          // 如果 canvas 没有被全部添加到 pdf，则新增一页空白 pdf
          pdf.addPage();
        page.remove(); // 移除新的画布
      }
      // 保存 pdf
      pdf.save(`${name}.pdf`);
    });
  }
}

export const alkColumns = () => {
  return [
    
    {
      title: '型号',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '容量(Nm³/h)',
      dataIndex: 'capacity',
      key: 'capacity',
    },
   
    {
      title: '电耗(kwh/Nm³)',
      dataIndex: 'power_consumption',
      key: 'power_consumption',
    },
    {
      title: '功率(MW)',
      dataIndex: 'pe',
      key: 'pe',
    },
    {
      title: '数量',
      dataIndex: 'number',
      key: 'number',
    },
  ]
}

export const paramsList = (topology = []) => {
  const result = [
    {
      title: '项目信息',
      params: [
        { label: '项目名称', name: 'projectName', unit: '' },
        { label: '客户名称', name: 'customer', unit: '' },
        { label: '项目周期', name: 'cycle', unit: '年' },
        { label: '年产氢量', name: 'h2Product', unit: '吨' },
        { label: '项目描述', name: 'projectDesc', unit: '' },
      ],
      visible: true,
    },
    {
      title: '光伏',
      params: [
        // { label: '容量范围', name: '', unit: 'MW' },
        { label: 'EPC投资', name: 'pv_epc', unit: '元/W' },
        { label: '运维成本', name: 'pv_om_cost', unit: '元/W/年' },
        { label: '输电损耗', name: 'pv_line_loss_rate', unit: '%' },
      ], 
      visible: !!topology[0]
    },
    {
      title: '风机',
      params: [
        // { label: '容量范围', name: '', unit: 'MW' },
        { label: 'EPC投资', name: 'wind_epc', unit: '元/W' },
        { label: '运维成本', name: 'wind_om_cost', unit: '元/W/年' },
        { label: '输电损耗', name: 'wind_line_loss_rate', unit: '%' },
      ],
      visible: !!topology[1]
    },
    {
      title: '电网',
      params: [
        { label: '年最大下网比例', name: 'grid_down_radio', unit: '%' },
        { label: '年最大上网比例', name: 'grid_up_radio', unit: '%' },
        { label: '绿电上网价格', name: 'grid_sale_price', unit: '元/kwh' },
        // { label: '网购电价', name: '', unit: '' },
      ],
      visible: !!topology[2]

    },
    {
      title: '储能',
      params: [
        // { label: '容量范围', name: '', unit: 'MWh' },
        { label: '最低配置比例', name: 'es_min_radio', unit: '%' },
        { label: 'EPC投资', name: 'es_epc', unit: '元/Wh' },
        { label: '运维成本', name: 'es_om_cost', unit: '元/Wh/年' },
      ],
      visible: !!topology[3]
    },
    {
      title: '电解槽',
      params: [
        // { label: '容量范围', name: '', unit: 'MW' },
        { label: '用水价格', name: 'water_price', unit: '元/吨' },
        { label: '制氢耗水量', name: 'h2_water_consuming', unit: 'L/Nm³' },
        { label: '运维比例', name: 'h2_om_radio', unit: '%' },
      ],
      visible: !!topology[4]
    },
    {
      title: '储罐',
      params: [
        { label: '最大升负荷速率', name: 'max_increase_load_rate', unit: '%'  },
        { label: '最大降负荷速率', name: 'max_down_load_rate', unit: '%'  },
        { label: '每小时供氢上限', name: 'max_supply_h2', unit: 'Nm³' },
        { label: '每小时供氢下限', name: 'min_supply_h2', unit: 'Nm³'  },
        { label: '负荷调整时间', name: 'adjust_time', unit: 'min' },
        { label: '负荷调节间隔', name: 'adjust_interval', unit: 'min' },
        { label: '每小时供氢量', name: 'supply_h2', unit: 'Nm³'  },
        { label: 'EPC投资', name: 'hs_invest', unit: '元/m³' },
        { label: '运维比例', name: 'hs_om_radio', unit: '%' },
      ],
      visible: !!topology[5]
    },
    {
      title: '其他配置',
      params: [
        { label: '贷款周期', name: 'loanCycle', unit: '年' },
        { label: '贷款比例', name: 'loanRadio', unit: '%' },
        { label: '贷款利率', name: 'loanRate', unit: '%' },
        { label: '设备折现率', name: 'discount_rate', unit: '%' },
        // { label: '负荷缺失率', name: 'load_miss', unit: '%' }
      ],
      visible: true
    },
  ]
  return result
}