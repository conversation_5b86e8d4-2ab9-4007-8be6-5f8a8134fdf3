<template>
  <div class="economic-report" :class="{ 'preview-mode': isPreviewMode }">
    <!-- 顶部标题和操作按钮 - 预览模式时隐藏 -->
    <div class="header-section" v-if="!isPreviewMode">
      <div class="title-wrapper">
        <a-breadcrumb>
          <a-breadcrumb-item><a @click="router.push({name: 'economicAnalysis'})"> < 经济分析</a></a-breadcrumb-item>
          <a-breadcrumb-item>方案报告</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div class="header-actions">
        <a-button class="action-btn" @click="downloadReport">下载报告</a-button>
      </div>
    </div>

    <a-spin :spinning="loading" :tip="tips">
      <!-- 报告标题 -->
      <h1 class="report-title">{{baseConfig.title}}方案</h1>

      <ProjectInfo :solution-info="solutionInfo" />

      <KeyIndicators :result-data="resultData" />

      <FlatTables :result-data="resultData" />

      <SensitivityAnalysis :sensitivity-data="sensitivityData" />

      <!-- 创建信息 -->
      <div class="report-footer">
        <div class="creator-info">
          <div class="creator-name">{{ baseConfig.company }}</div>
          <div class="generation-time">{{ currentTime }}</div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getEconomicSolutionResult, getEconomicSolutionInfo } from '@/api/economic'
import ProjectInfo from '../EconomicDetail/components/ProjectInfo.vue'
import KeyIndicators from '../EconomicDetail/components/KeyIndicators.vue'
import FlatTables from './components/FlatTables.vue'
import SensitivityAnalysis from '../EconomicDetail/components/SensitivityAnalysis.vue'
import { downloadFile, strToBase64 } from '@/util'
import { baseConfig } from '@/config/baseConfig'

const router = useRouter()
const route = useRoute()
const resultData = ref(null)
const sensitivityData = ref(null)
const solutionInfo = ref({})
const loading = ref(false)
const currentTime = ref('')
const tips = ref('数据加载中...')

// 检查是否为预览模式
const isPreviewMode = computed(() => {
  return route.query.type === '1'
})

const getResultData = async () => {
  try {
    loading.value = true
    // 从 query 参数获取，而不是 params
    const projectId = route.query.projectId
    const solutionId = route.query.solutionId
    
    console.log('路由参数:', { projectId, solutionId })
    
    if (!projectId || !solutionId) {
      console.error('缺少必要的路由参数')
      return
    }
    
    const { code, data, msg} = await getEconomicSolutionResult({
      projectId: Number(projectId),
      solutionId: Number(solutionId)
    })
    console.log('接口返回数据:', data)
    console.log('敏感性分析数据:', data.resultAnalysis)
    if (code === 0) {
      resultData.value = data.resultTables;
      // 设置敏感性分析数据
      sensitivityData.value = data.resultAnalysis || null;
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    resultData.value = null
  } finally {
    loading.value = false
  }
}

const getSolutionInfo = async () => {
  try {
    // 从 query 参数获取，而不是 params
    const projectId = route.query.projectId
    const solutionId = route.query.solutionId
    
    if (!projectId || !solutionId) {
      console.error('缺少必要的路由参数')
      return
    }
    
    const { code, data, msg } = await getEconomicSolutionInfo({
      projectId: Number(projectId),
      solutionId: Number(solutionId)
    })
    if (code === 0) {
      solutionInfo.value = data
    }
  } catch (error) {
    console.error('获取方案信息失败:', error)
  }
}

// 设置静态时间（只到分钟）
const setCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const downloadReport = async () => {
  loading.value = true
  tips.value = '报告生成中...'
  const str = await strToBase64(`${location.origin}${location.pathname}${location.search}&type=1`)
  await downloadFile(`/api/v1/cecp/result/export?encodedUrl=${str}`, `${solutionInfo.value?.project?.name}.pdf`)

  setTimeout(() => {
    loading.value = false
  }, 6500);
}

onMounted(async () => {
  // 设置静态时间
  setCurrentTime()
  
  await Promise.all([
    getResultData(),
    getSolutionInfo()
  ])
})
</script>

<style scoped>
.economic-report {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 4px;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  height: 32px;
  padding: 0 15px;
  font-size: 14px;
}

.title-wrapper {
  flex: 1;
}

.report-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 24px 0;
  text-align: center;
  letter-spacing: 1px;
}

.report-footer {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
}

.creator-info {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  text-align: right;
  margin-right: 20px;
}

.creator-name {
  font-weight: 500;
  color: #333;
}

.generation-time {
  margin-top: 4px;
}

/* 预览模式样式 */
.economic-report.preview-mode {
  padding: 16px 0;
  background: white;
}
</style>