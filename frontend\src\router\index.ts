import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@/layout/MainLayout/index.vue';
import NoMenuLayout from '@/layout/NoMenuLayout/index.vue'
import SideMenuLayout from '@/layout/SideMenuLayout/index.vue';
import DarkSideMenuLayout from '@/layout/DarkSideMenuLayout/index.vue';
import { initRouterHook } from './routerHook'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      // name: 'home',
      redirect: to => ({
        name: 'home'
      }),
      component: MainLayout, //   NoMenuLayout  MainLayout SideMenuLayout DarkSideMenuLayout
      children: [
        {
          path: 'index',
          name: 'home',
          component: () => import('@/views/Home/index.vue'),
          meta: { key: 'home' }
        },
        {
          path: 'test-expand',
          name: 'testExpand',
          component: () => import('@/views/TestExpandCollapse.vue'),
          meta: { key: 'testExpand' }
        }
      ],
    },
    {
      path: '/project',
      component: NoMenuLayout, //   NoMenuLayout  MainLayout SideMenuLayout DarkSideMenuLayout
      children: [
        {
          path: 'detail',
          name: 'projectDetail',
          component: () => import('@/views/ProjectDetail/index.vue'),
          meta: { key: 'projectDetail' }
        },
        {
          path: 'create',
          name: 'createProject',
          component: () => import('@/views/CreateProject/index.vue'),
          meta: { key: 'createProject' }
        }
      ]
    },
    {
      path: '/project',
      component: MainLayout, //   NoMenuLayout  MainLayout SideMenuLayout DarkSideMenuLayout
      children: [
        {
          path: 'list',
          name: 'projectList',
          component: () => import('@/views/ProjectList/index.vue'),
          meta: { key: 'projectList' }
        }
      ]
    },
    {
      path: '/device',
      component: MainLayout, //   NoMenuLayout  MainLayout SideMenuLayout DarkSideMenuLayout
      children: [
        {
          path: 'config',
          name: 'deviceConfig',
          component: () => import('@/views/DeviceConfig/index.vue'),
          meta: { key: 'deviceConfig' }
        }
      ]
    },
    {
      path: '/manage',
      component: MainLayout, //   NoMenuLayout  MainLayout SideMenuLayout DarkSideMenuLayout
      children: [
        {
          path: 'index',
          name: 'manage',
          component: () => import('@/views/Manage/index.vue'),
          meta: { key: 'manage' }
        },
        {
          path: 'monitor',
          name: 'monitor',
          component: () => import('@/views/Monitor/index.vue'),
          meta: { key: 'monitor' }
        }
      ]
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/Login/index.vue'),
      meta: { key: 'login' }
    },
    {
      path: '/economic',
      component: MainLayout, // 使用新的深色侧边栏布局作为示例
      children: [
        {
          path: 'index',
          name: 'economicAnalysis',
          component: () => import('@/views/EconomicList/index.vue'),
          meta: { key: 'economicAnalysis' }
        },
      ]
    },
    {
      path: '/economic',
      component: NoMenuLayout, //   NoMenuLayout  MainLayout
      children: [
        {
          path: 'create',
          name: 'economicAnalysisCreate',
          component: () => import('@/views/EconomicCreate/index.vue'),
          meta: { key: 'economicAnalysis' }
        },
        {
          path: 'detail/:projectId/:solutionId',
          name: 'economicDetail',
          component: () => import('@/views/EconomicDetail/index.vue'),
          meta: { key: 'economicDetail' }
        },
        {
          path: 'compare',
          name: 'economicCompare',
          component: () => import('@/views/EconomicCompare/index.vue'),
          meta: { key: 'economicCompare' }
        }
      ]
    },
    {
      path: '/report-detail',
      name: 'reportDetail',
      component: () => import('@/views/EconomicReport/index.vue'),
      meta: { key: 'reportDetail' }
    }
  ]
})

initRouterHook(router)

export default router
