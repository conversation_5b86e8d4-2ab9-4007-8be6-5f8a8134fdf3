<template>
  <div class="pv-device-editor">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="厂商" name="manufacturer">
        <a-input 
          v-model:value="formData.manufacturer" 
          placeholder="请输入厂商名称" 
          :disabled="disabled"
        />
      </a-form-item>
      
      <a-form-item label="产品型号" name="model">
        <a-input 
          v-model:value="formData.model" 
          placeholder="请输入产品型号（可选）" 
          :disabled="disabled"
        />
      </a-form-item>
      
      <a-form-item label="衰减率配置" name="dampCurve">
        <div class="damp-curve-section">
          <!-- 时间段编辑区域 -->
          <div class="periods-editor">
            <div class="editor-header">
              <span class="section-title">时间段配置</span>
              <a-button
                type="primary"
                size="small"
                @click="addPeriod"
                :disabled="disabled"
              >
                新增时间段
              </a-button>
            </div>

            <!-- 时间段列表 -->
            <div class="periods-list">
              <div
                v-for="(period, index) in periods"
                :key="index"
                class="period-row"
              >
                <div class="period-label">衰减率</div>
                <div class="period-inputs">
                  <span class="year-label">第</span>
                  <a-input-number
                    v-model:value="period.startYear"
                    :min="1"
                    size="small"
                    style="width: 60px;"
                    :disabled="disabled"
                  />
                  <span class="year-label">年</span>
                  <span class="separator">至</span>
                  <span class="year-label">第</span>
                  <a-input-number
                    v-model:value="period.endYear"
                    :min="1"
                    size="small"
                    style="width: 60px;"
                    :disabled="disabled"
                  />
                  <span class="year-label">年</span>
                  <a-input-number
                    v-model:value="period.dampRate"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    size="small"
                    style="width: 80px;"
                    :disabled="disabled"
                  />
                  <a-button 
                    size="small" 
                    danger 
                    type="text"
                    @click="removePeriod(index)"
                    :disabled="periods.length <= 1 || disabled"
                  >
                    删除
                  </a-button>
                </div>
              </div>
            </div>
          </div>
          

        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      manufacturer: '',
      model: '',
      dampCurve: []
    })
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()

// 表单数据
const formData = ref({
  manufacturer: '',
  model: ''
})

// 时间段数据
const periods = ref([
  {
    startYear: 1,
    endYear: 25,
    dampRate: 0.95
  }
])

// 衰减率曲线数据（用于图表显示）
const dampCurveData = computed(() => {
  if (periods.value.length === 0) return []

  const maxYear = Math.max(...periods.value.map(p => p.endYear || 0), 25)
  const data = new Array(maxYear).fill(0)

  periods.value.forEach(period => {
    if (period.startYear && period.endYear && period.dampRate !== null) {
      for (let year = period.startYear; year <= period.endYear; year++) {
        if (year <= maxYear && year >= 1) {
          data[year - 1] = period.dampRate
        }
      }
    }
  })

  return data
})

// 表单验证规则
const rules = {
  manufacturer: [
    { required: true, message: '请输入厂商名称', trigger: 'blur' }
  ],
  model: [
    // 产品型号为可选字段
  ]
}

// 添加时间段
const addPeriod = () => {
  const lastPeriod = periods.value[periods.value.length - 1]
  const startYear = lastPeriod ? lastPeriod.endYear + 1 : 1

  periods.value.push({
    startYear,
    endYear: startYear + 1,
    dampRate: 0.95
  })
}

// 删除时间段
const removePeriod = (index) => {
  if (periods.value.length <= 1) {
    message.warning('至少需要保留一个时间段')
    return
  }

  periods.value.splice(index, 1)
}

// 验证时间段连续性（仅在确认时调用）
const validatePeriods = () => {
  const errors = []

  // 检查每个时间段的基本有效性
  for (let i = 0; i < periods.value.length; i++) {
    const period = periods.value[i]

    if (!period.startYear || period.startYear < 1) {
      errors.push(`时段${i + 1}：开始年份无效`)
    }

    if (!period.endYear || period.endYear < 1) {
      errors.push(`时段${i + 1}：结束年份无效`)
    }

    if (period.dampRate === null || period.dampRate === undefined || period.dampRate < 0 || period.dampRate > 1) {
      errors.push(`时段${i + 1}：衰减率必须在0-1之间`)
    }

    if (period.startYear && period.endYear && period.startYear > period.endYear) {
      errors.push(`时段${i + 1}：开始年份不能大于结束年份`)
    }
  }

  if (errors.length > 0) {
    return errors
  }

  // 检查时间段连续性
  const sortedPeriods = [...periods.value].sort((a, b) => a.startYear - b.startYear)

  // 检查是否从第1年开始
  if (sortedPeriods[0].startYear !== 1) {
    errors.push('时间段必须从第1年开始')
  }

  // 检查连续性
  for (let i = 0; i < sortedPeriods.length - 1; i++) {
    const current = sortedPeriods[i]
    const next = sortedPeriods[i + 1]

    if (current.endYear + 1 !== next.startYear) {
      if (current.endYear >= next.startYear) {
        errors.push(`时段${i + 1}和时段${i + 2}存在重叠`)
      } else {
        errors.push(`时段${i + 1}和时段${i + 2}之间存在间隔`)
      }
    }
  }

  return errors
}

// 从外部数据初始化时间段（用于编辑回显）
const initializeFromData = (data) => {
  if (!data || data.length === 0) {
    periods.value = [{
      startYear: 1,
      endYear: 25,
      dampRate: 0.95
    }]
    return
  }

  // 从衰减率数组反推时间段配置，合并相同值的连续段
  const newPeriods = []
  let currentValue = data[0]
  let startYear = 1

  for (let i = 1; i <= data.length; i++) {
    // 只有当值发生变化或到达数组末尾时才创建新时间段
    if (i === data.length || data[i] !== currentValue) {
      newPeriods.push({
        startYear,
        endYear: i,
        dampRate: currentValue
      })

      if (i < data.length) {
        currentValue = data[i]
        startYear = i + 1
      }
    }
  }

  periods.value = newPeriods.length > 0 ? newPeriods : [{
    startYear: 1,
    endYear: data.length,
    dampRate: data[0] || 0.95
  }]
}

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    formData.value.manufacturer = newValue.manufacturer || ''
    formData.value.model = newValue.model || ''

    if (newValue.dampCurve && newValue.dampCurve.length > 0) {
      initializeFromData(newValue.dampCurve)
    } else {
      initializeFromData([])
    }
  }
}, { immediate: true, deep: true })

// 验证表单（包含时间段连续性校验）
const validate = async () => {
  try {
    // 先验证基本表单字段
    await formRef.value.validateFields()

    // 再验证时间段连续性
    const periodErrors = validatePeriods()
    if (periodErrors.length > 0) {
      message.error(`时间段配置错误：${periodErrors.join('；')}`)
      return false
    }

    return true
  } catch (error) {
    console.log('表单验证失败:', error)
    return false
  }
}

// 重置表单
const resetFields = () => {
  formData.value = {
    manufacturer: '',
    model: ''
  }
  periods.value = [{
    startYear: 1,
    endYear: 25,
    dampRate: 0.95
  }]
  formRef.value?.resetFields()
}

// 获取表单数据
const getFormData = () => {
  return {
    type: 1, // 光伏设备类型
    baseInfo: {
      manufacturer: formData.value.manufacturer,
      model: formData.value.model
    },
    params: {
      damp_curve: {
        data: dampCurveData.value
      }
    }
  }
}

// 暴露方法
defineExpose({
  validate,
  resetFields,
  getFormData
})
</script>

<style scoped>
.pv-device-editor {
  padding: 0;
}

.damp-curve-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.curve-preview {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  background: white;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.preview-content {
  text-align: center;
}

/* 内联编辑样式 */
.periods-editor {
  margin-bottom: 16px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-weight: 500;
  color: #333;
}

.periods-list {
  margin-bottom: 12px;
}

.period-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background: #fafafa;
}

.period-label {
  width: 60px;
  line-height: 24px;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
}

.period-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.year-label {
  font-size: 12px;
  color: #666;
  line-height: 24px;
}

.separator {
  font-size: 12px;
  color: #666;
  margin: 0 4px;
}

.period-errors {
  margin-top: 4px;
  width: 100%;
}

.error-text {
  color: #ff4d4f;
  font-size: 11px;
  line-height: 1.2;
  margin-bottom: 2px;
}

.validation-summary {
  margin-bottom: 12px;
}

.error-list {
  margin: 0;
  padding-left: 16px;
}

.error-list li {
  margin-bottom: 4px;
  color: #ff4d4f;
  font-size: 12px;
}
</style>
