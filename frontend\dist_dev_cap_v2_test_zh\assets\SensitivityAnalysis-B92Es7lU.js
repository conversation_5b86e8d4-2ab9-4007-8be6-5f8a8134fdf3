import{_,e as A,b as u,c as k,g as s,F as x,l as b,t as p,E as P,G as T,i as C,f as F,h as L}from"./index-CtrMLNJH.js";const E=e=>(P("data-v-f9ad475f"),e=e(),T(),e),N={class:"project-info"},W=E(()=>s("h2",{class:"section-title"},"项目信息",-1)),S={class:"info-grid"},j={class:"label"},O={class:"value"},D={__name:"ProjectInfo",props:{solutionInfo:{type:Object,default:()=>({})}},setup(e){const i=e,y=A(()=>{var a,r,m,I,h,n,c,d,f,v;return[{label:"项目名称：",value:(r=(a=i.solutionInfo)==null?void 0:a.project)==null?void 0:r.name,type:"static"},{label:"客户名称：",value:(I=(m=i.solutionInfo)==null?void 0:m.project)==null?void 0:I.customer,type:"static"},{label:"项目周期：",value:`${(n=(h=i.solutionInfo)==null?void 0:h.calcParams)==null?void 0:n.operatingYears}年`,type:"static"},{label:"贷款周期：",value:`${(d=(c=i.solutionInfo)==null?void 0:c.calcParams)==null?void 0:d.loanTerm}年`,type:"static"},{label:"项目描述：",value:(v=(f=i.solutionInfo)==null?void 0:f.project)==null?void 0:v.desc,type:"static"}]});return(a,r)=>(u(),k("div",N,[W,s("div",S,[(u(!0),k(x,null,b(y.value,m=>(u(),k("div",{key:m.label,class:"info-item"},[s("span",j,p(m.label),1),s("span",O,p(m.value),1)]))),128))])]))}},ce=_(D,[["__scopeId","data-v-f9ad475f"]]),l=e=>e==null||isNaN(e)?"--":new Intl.NumberFormat("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e),o=e=>e==null||isNaN(e)?"--":e==1/0?1/0:(e*100).toFixed(2),t=e=>e==null||isNaN(e)?"--":e==1/0?1/0:e.toFixed(3),ue={keyIndicator:[{title:"指标名称",dataIndex:"name",key:"name",width:250},{title:"数值",dataIndex:"value",key:"value",width:150},{title:"说明",dataIndex:"description",key:"description"}],fixedAssets:[{title:"项目",dataIndex:"item",key:"item",width:250},{title:"投资额",dataIndex:"amount",key:"amount",width:150}],projectBudget:[{title:"项目",dataIndex:"item",key:"item",width:250},{title:"金额",dataIndex:"amount",key:"amount",width:150}],financing:[{title:"项目",dataIndex:"item",key:"item",width:250},{title:"金额",dataIndex:"amount",key:"amount",width:150}],investmentPlan:[{title:"项目",dataIndex:"item",key:"item",width:250},{title:"金额",dataIndex:"amount",key:"amount",width:150}],financialSummary:[{title:"指标名称",dataIndex:"name",key:"name",width:250},{title:"数值",dataIndex:"value",key:"value",width:150}]},ke=(e=25)=>{const i=[{title:"指标",dataIndex:"indicator",key:"indicator",fixed:"left",width:200}];for(let y=1;y<=e;y++)i.push({title:`第${y}年`,dataIndex:`year${y}`,key:`year${y}`,width:100});return i},de={keyIndicators:[{key:"1",nameKey:"loanRate",name:"借款利率",formatter:o},{key:"2",nameKey:"totalEducationSurchargeRate",name:"总教育费附加税率",formatter:o}],fixedAssets:[{key:"7",nameKey:"projectTotalInvestment",name:"项目总投资(万元)"},{key:"1",nameKey:"mechanicalAndElectricalEquipment",name:"机电设备及安装工程(万元)"},{key:"2",nameKey:"constructionWorks",name:"建筑工程(万元)"},{key:"3",nameKey:"otherExpenses",name:"其他费用(万元)"},{key:"4",nameKey:"fixedAssetsStaticInvestment",name:"固定资产静态投资(万元)"},{key:"5",nameKey:"workingCapital",name:"流动资金(万元)"},{key:"6",nameKey:"constructionPeriodInterest",name:"建设期利息(万元)"},{key:"10",nameKey:"fixedAssetsNoTax",name:"固定资产不含税(万元)"},{key:"13",nameKey:"fixedAssetsWithTax",name:"固定资产含税(万元)"},{key:"14",nameKey:"equipmentAndInstallationNoTax",name:"设备及安装不含税(万元)"},{key:"19",nameKey:"equipmentPurchaseCost",name:"设备购置费(万元)"},{key:"20",nameKey:"installationCost",name:"安装工程费(万元)"}],projectBudget:[{key:"1",nameKey:"mechanicalAndElectricalEquipment",name:"机电设备及安装工程(万元)"},{key:"2",nameKey:"constructionWorks",name:"建筑工程(万元)"},{key:"3",nameKey:"otherExpenses",name:"其他费用(万元)"},{key:"4",nameKey:"staticInvestment",name:"静态投资(万元)"},{key:"5",nameKey:"constructionPeriodSubsidy",name:"建设期补助(万元)"},{key:"6",nameKey:"constructionInvestment",name:"建设投资(万元)"},{key:"7",nameKey:"constructionPeriodInterest",name:"建设期利息(万元)"},{key:"8",nameKey:"totalProjectInvestment",name:"工程总投资(万元)"},{key:"9",nameKey:"dynamicInvestmentPerWatt",name:"单瓦动态总投资(元/W)"},{key:"10",nameKey:"staticInvestmentPerWatt",name:"单瓦静态总投资(元/W)"}],financing:[{key:"1",nameKey:"projectTotalInvestment",name:"项目总投资(万元)"},{key:"2",nameKey:"bankLoan",name:"银行借款(万元)"},{key:"3",nameKey:"equityCapital",name:"资本金(万元)"}],investmentPlan:[{key:"1",nameKey:"projectTotalInvestment",name:"项目总投资(万元)"},{key:"2",nameKey:"fixedAssetsStaticInvestment",name:"固定资产静态投资(万元)"},{key:"3",nameKey:"foreignInvestment",name:"外商投资(万元)"},{key:"4",nameKey:"domesticInvestment",name:"国内投资(万元)"},{key:"5",nameKey:"constructionPeriodInterest",name:"建设期利息(万元)"},{key:"6",nameKey:"foreignInterestDuringConstruction",name:"外商建设期利息(万元)"},{key:"7",nameKey:"domesticInterestDuringConstruction",name:"国内建设期利息(万元)"},{key:"8",nameKey:"workingCapital",name:"流动资金(万元)"},{key:"9",nameKey:"fundRaising",name:"资金筹措(万元)"},{key:"10",nameKey:"equityCapital",name:"资本金(万元)"},{key:"11",nameKey:"bankLoan",name:"银行借款(万元)"},{key:"12",nameKey:"longTermLoan",name:"长期借款(万元)"},{key:"13",nameKey:"domesticLoan",name:"国内借款(万元)"},{key:"14",nameKey:"foreignLoan",name:"外商借款(万元)"},{key:"15",nameKey:"constructionPeriodInterestLoan",name:"建设期利息借款(万元)"},{key:"16",nameKey:"workingCapitalLoan",name:"流动资金借款(万元)"}],financialSummary:[{key:"1",nameKey:"installedCapacity",name:"装机规模(MW)"},{key:"2",nameKey:"operatingYears",name:"项目经营期(年)"},{key:"3",nameKey:"averageAnnualGeneration",name:"年均发电量(万kW·h)",formatter:t},{key:"4",nameKey:"totalGridElectricity",name:"上网总结算电量(万kW·h)",formatter:t},{key:"5",nameKey:"averageAnnualGridElectricity",name:"年均上网结算电量(万kW·h)",formatter:t},{key:"6",nameKey:"annualEffectiveUtilizationHours",name:"年有效利用小时数(小时)",formatter:t},{key:"7",nameKey:"projectTotalInvestment",name:"项目总投资(万元)",formatter:t},{key:"8",nameKey:"fixedAssetsStaticInvestment",name:"固定资产静态投资(万元)",formatter:t},{key:"9",nameKey:"constructionPeriodInterest",name:"建设期利息(万元)",formatter:t},{key:"10",nameKey:"workingCapital",name:"流动资金(万元)",formatter:t},{key:"11",nameKey:"staticInvestmentPerWatt",name:"单瓦静态总投资(元/W)",formatter:t},{key:"12",nameKey:"dynamicInvestmentPerWatt",name:"单瓦动态总投资(元/W)",formatter:t},{key:"13",nameKey:"investmentCostPerKWh",name:"度电投资成本(元/kWh)",formatter:t},{key:"14",nameKey:"equityCapitalAmount",name:"项目资本金投入金额(万元)",formatter:t},{key:"15",nameKey:"totalBankLoanAmount",name:"项目银行贷款总额(万元)",formatter:t},{key:"16",nameKey:"totalSalesRevenue",name:"销售收入总额(万元)",formatter:t},{key:"17",nameKey:"totalCostExpense",name:"总成本费用(万元)",formatter:t},{key:"18",nameKey:"averageAnnualOperatingCost",name:"年平均运营成本(万元)",formatter:t},{key:"19",nameKey:"totalGenerationCostPerUnit",name:"单位发电总成本(元/kWh)",formatter:t},{key:"20",nameKey:"operatingCostPerKWh",name:"度电运营成本(元/kWh)",formatter:t},{key:"21",nameKey:"totalSalesTaxSurcharges",name:"销售税金附加总额(万元)",formatter:t},{key:"22",nameKey:"totalPowerGenerationProfit",name:"发电利润总额(万元)",formatter:t},{key:"23",nameKey:"levelizedCostOfElectricity",name:"平准化度电成本LCOE(元/kWh)",formatter:t},{key:"24",nameKey:"paybackPeriodAfterTax_static",name:"税后投资回收期(静态)(年)",formatter:t},{key:"25",nameKey:"paybackPeriodAfterTax_dynamic",name:"税后投资回收期(动态)(年)",formatter:t},{key:"26",nameKey:"projectInvestmentFIRR_beforeTax",name:"项目投资财务内部收益率_税前(%)",formatter:o},{key:"27",nameKey:"projectInvestmentFIRR_afterTax",name:"项目投资财务内部收益率_税后(%)",formatter:o},{key:"28",nameKey:"equityCapitalFIRR_beforeTax",name:"资本金财务内部收益率_税前(%)",formatter:o},{key:"29",nameKey:"equityCapitalFIRR_afterTax",name:"资本金财务内部收益率_税后(%)",formatter:o},{key:"30",nameKey:"hydrogenPrice",name:"氢气价格(元/kg)",formatter:t},{key:"31",nameKey:"projectInvestmentNPV_beforeTax",name:"项目投资财务税前净现值(万元)",formatter:t},{key:"32",nameKey:"equityCapitalNPV_afterTax",name:"资本金财务税后净现值(万元)",formatter:t},{key:"33",nameKey:"returnOnInvestment",name:"总投资收益率ROI(%)",formatter:o},{key:"34",nameKey:"investmentTaxRate",name:"投资利税率(%)",formatter:o},{key:"35",nameKey:"returnOnEquity",name:"项目资本金净利润率ROE(%)",formatter:o},{key:"36",nameKey:"assetLiabilityRatio",name:"资产负债率(%)",formatter:o},{key:"37",nameKey:"vatRefund50Percent",name:"增值税即征即退50%(万元)",formatter:t},{key:"38",nameKey:"lcoe_ori",name:"平准化度电成本(元/kWh)",formatter:t},{key:"39",nameKey:"lcoh_ori",name:"平准化制氢成本(元/Nm³)",formatter:t}]},fe={annualHydrogenAndGridPower:[{key:"yearDegradationPercentage",name:"年衰减百分值(%)",isPercentage:!0},{key:"componentDegradationEfficiency",name:"组件衰减效率",isDecimal:!0},{key:"powerLimitationRate",name:"限电率(%)",isPercentage:!0},{key:"powerGenerationRate",name:"发电率(%)",isPercentage:!0},{key:"predictedPowerGenerationAfterDegradation",name:"预计发电量(衰减后)(万度)"},{key:"predictedPowerGenerationWithPowerLimitation",name:"预计发电量(衰减+限电)(万度)"},{key:"gridElectricity",name:"上网电量(万度)"},{key:"greenHydrogenElectricity",name:"绿电制氢电量(万度)"},{key:"powerDiscountFactor",name:"发电折现系数",isDecimal:!0},{key:"discountedPowerGeneration",name:"发电量折现值(万度)"},{key:"gridHydrogenElectricity",name:"下网制氢电量(万度)"},{key:"hydrogenProduction",name:"制氢量(万公斤)"},{key:"oxygenProduction",name:"制氧量(万公斤)"},{key:"waterConsumption",name:"耗水量(万吨)"},{key:"greenPowerRatioAfterYears",name:"绿电衰减后可用比例(%)",isPercentage:!0}],loanRepaymentSchedule:[{key:"longTermLoan",name:"长期借款(万元)"},{key:"beginningLoanBalance",name:"年初借款余额(万元)"},{key:"annualRepayment",name:"本年还本(万元)"},{key:"annualInterest",name:"本年付息(万元)"},{key:"endingLoanBalance",name:"期末借款余额(万元)"},{key:"currentRepaymentAndInterest",name:"当期还本付息(万元)"},{key:"workingCapitalLoanRepayment",name:"偿还流动资金借款本金(万元)"},{key:"shortTermLoanRepayment",name:"偿还短期借款本金(万元)"},{key:"workingCapitalInterest",name:"流动资金利息(万元)"},{key:"shortTermLoanInterest",name:"短期借款利息(万元)"},{key:"shortTermLoan",name:"短期借款(万元)"},{key:"loanPrincipalRepayment",name:"偿还借款本金(万元)"},{key:"workingCapitalLoan",name:"流动资金借款(万元)"},{key:"loanInterestRepayment",name:"偿还借款利息(万元)"},{key:"totalLoanPayment",name:"借款本息合计(万元)"}],totalCostAndExpenses:[{key:"depreciation",name:"折旧费(万元)"},{key:"maintenanceCost",name:"维修费(万元)"},{key:"insuranceCost",name:"保险费(万元)"},{key:"waterCost",name:"水费(万元)"},{key:"electricityCost",name:"电费(万元)"},{key:"materialsCost",name:"材料费(万元)"},{key:"transportationCost",name:"运输费(万元)"},{key:"equipmentOverhaulReplacementCost",name:"设备大修更换费(万元)"},{key:"landRentalFee",name:"土地租赁费(万元)"},{key:"povertyAlleviationFee",name:"扶贫规费支出(万元)"},{key:"landTaxFee",name:"土地税费(万元)"},{key:"otherCost",name:"其他费用(万元)"},{key:"publicLoadPowerCost",name:"公辅装置用电成本(万元)"},{key:"waterResourceTax",name:"水资源税(万元)"},{key:"electrolyzerWaterResourceTax",name:"电解槽水资源税(万元)"},{key:"laborCost",name:"工资福利及劳保统筹和住房基金(万元)"},{key:"amortizationCost",name:"摊销费(万元)"},{key:"maintenanceCostInputTax",name:"维修费进项税(万元)"},{key:"materialsCostInputTax",name:"材料费进项税(万元)"},{key:"transportationCostInputTax",name:"运输费进项税(万元)"},{key:"waterCostInputTax",name:"水费进项税(万元)"},{key:"electricityCostInputTax",name:"电费进项税(万元)"},{key:"inputTax",name:"进项税(万元)"},{key:"interestExpenseWithTax",name:"含税利息支出(万元)"},{key:"fixedCost",name:"固定成本(万元)"},{key:"variableCost",name:"可变成本(万元)"},{key:"totalCostExpense",name:"总成本费用(万元)"}],profitAndProfitDistribution:[{key:"nonTaxOperatingIncome",name:"不含税营业收入(万元)"},{key:"outputTax",name:"销项税(万元)"},{key:"inputTaxEndingBalance",name:"进项税期末余额(万元)"},{key:"inputTaxBeginningBalance",name:"进项税期初余额(万元)"},{key:"currentPeriodInputTax",name:"本期进项税额(万元)"},{key:"inputTaxDeduction",name:"进项税抵扣额(万元)"},{key:"valueAddedTax",name:"应缴税金(万元)"},{key:"inputTaxRemainingAmount",name:"进项税留底金额(万元)"},{key:"cityConstructionTax",name:"城建税(万元)"},{key:"totalEducationSurcharge",name:"总教育费附加(万元)"},{key:"localEducationSurcharge",name:"地方教育费附加(万元)"},{key:"salesTaxAndAdditions",name:"销售税金及附加(万元)"},{key:"taxableSubsidy",name:"补贴收入应税(万元)"},{key:"taxExemptSubsidy",name:"补贴收入免税(万元)"},{key:"totalProfit",name:"利润总额(万元)"},{key:"accumulatedTotalProfit",name:"累计利润总额(万元)"},{key:"previousYearLossCompensation",name:"弥补以前年度亏损(万元)"},{key:"taxableIncome",name:"应纳税所得额(万元)"},{key:"incomeTax",name:"所得税(万元)"},{key:"netProfit",name:"净利润(万元)"},{key:"initialUndistributedProfit",name:"期初未分配的利润(万元)"},{key:"statutoryReserveFund",name:"提取法定盈余公积金(万元)"},{key:"profitAvailableForDistribution",name:"可供投资者分配的利润(万元)"},{key:"payableProfit",name:"应付利润(万元)"},{key:"undistributedProfit",name:"未分配利润(万元)"},{key:"interestAndTaxProfit",name:"息税前利润(万元)"},{key:"EBITDA",name:"息税折旧摊销前利润(万元)"}],projectInvestmentCashFlow:[{key:"operatingIncomeWithTax",name:"含税营业收入(万元)"},{key:"subsidyIncome",name:"补贴收入(万元)"},{key:"fixedAssetsResidualValue",name:"固定资产残值(万元)"},{key:"workingCapitalRecovery",name:"流动资金回收(万元)"},{key:"projectInvestmentCashInflow",name:"项目投资现金流入(万元)"},{key:"constructionInvestment",name:"建设投资(万元)"},{key:"workingCapitalInvestment",name:"流动资金投资(万元)"},{key:"operatingCost",name:"经营成本(万元)"},{key:"valueAddedTaxInputPayment",name:"增值税进项税额(万元)"},{key:"valueAddedTaxPayment",name:"增值税缴纳(万元)"},{key:"projectInvestmentCashOutflow",name:"项目投资现金流出(万元)"},{key:"netCashFlowBeforeIncomeTax",name:"所得税前净现金流量(万元)"},{key:"accumulatedNetCashFlowBeforeIncomeTax",name:"累计所得税前净现金流量(万元)"},{key:"adjustedIncomeTax",name:"调整所得税(万元)"},{key:"netCashFlowAfterIncomeTax",name:"所得税后净现金流量(万元)"},{key:"accumulatedNetCashFlowAfterIncomeTax",name:"累计所得税后净现金流量(万元)"},{key:"dynamicPaybackPeriod_discountFactor",name:"动态回收期折现系数",isDecimal:!0},{key:"dynamicPaybackPeriod_presentValueBeforeTax",name:"动态回收期税前现值(万元)"},{key:"dynamicPaybackPeriod_presentValueAfterTax",name:"动态回收期税后现值(万元)"},{key:"dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax",name:"动态回收期累计税前净现金流量(万元)"},{key:"dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax",name:"动态回收期累计税后净现金流量(万元)"},{key:"accountsReceivable",name:"应收账款(万元)"},{key:"interestCoverageRatio",name:"利息覆盖率(倍数)"},{key:"debtCoverageRatio",name:"偿债覆盖率(倍数)"}],equityCapitalCashFlow:[{key:"operatingIncomeWithTax",name:"含税营业收入(万元)"},{key:"subsidyIncome",name:"补贴收入(万元)"},{key:"projectCapitalInvestment",name:"项目资本金投资(万元)"},{key:"loanPrincipalRepayment",name:"偿还借款本金(万元)"},{key:"annualInterest",name:"本年付息(万元)"},{key:"shortTermLoanInterest",name:"短期借款利息(万元)"},{key:"workingCapitalInterest",name:"流动资金利息(万元)"},{key:"operatingCost",name:"经营成本(万元)"},{key:"inputTaxPayment",name:"进项税支付(万元)"},{key:"salesTaxAndSurcharge",name:"销售税金及附加(万元)"},{key:"incomeTax",name:"所得税(万元)"},{key:"vatPayable",name:"应缴增值税(万元)"},{key:"fixedAssetsResidualValue",name:"固定资产残值(万元)"},{key:"workingCapitalRecovery",name:"流动资金回收(万元)"},{key:"payableProfit",name:"应付利润(万元)"},{key:"loanInterestRepayment",name:"偿还借款利息(万元)"},{key:"capitalCashInflow",name:"资本金现金流入(万元)"},{key:"capitalCashOutflow",name:"资本金现金流出(万元)"},{key:"capitalNetCashFlow",name:"资本金净现金流量(万元)"},{key:"accumulatedNetCashFlow",name:"累计净现金流量(万元)"},{key:"capitalPretaxNetCashFlow",name:"资本金税前净现金流量(万元)"},{key:"accumulatedPretaxNetCashFlow",name:"累计税前净现金流量(万元)"},{key:"discountFactor",name:"折现系数",isDecimal:!0},{key:"presentValue",name:"现值(万元)"},{key:"accumulatedPresentValue",name:"累计现值(万元)"}],financialPlanCashFlow:[{key:"operatingIncomeWithTax",name:"含税营业收入(万元)"},{key:"salesTax",name:"销项税(万元)"},{key:"taxableSubsidyIncome",name:"应税补贴收入(万元)"},{key:"operatingActivityOtherInflow",name:"经营活动其他流入(万元)"},{key:"operatingActivityCashInflow",name:"经营活动现金流入(万元)"},{key:"operatingCost",name:"经营成本(万元)"},{key:"inputTax",name:"进项税(万元)"},{key:"salesTaxAndSurcharge",name:"销售税金及附加(万元)"},{key:"vatPayable",name:"应缴增值税(万元)"},{key:"incomeTax",name:"所得税(万元)"},{key:"operatingActivityOtherOutflow",name:"经营活动其他流出(万元)"},{key:"operatingActivityCashOutflow",name:"经营活动现金流出(万元)"},{key:"operatingActivitiesNetCashFlow",name:"经营活动净现金流量(万元)"},{key:"investmentActivityCashInflow",name:"投资活动现金流入(万元)"},{key:"fixedAssetsResidualValue",name:"固定资产残值(万元)"},{key:"workingCapitalRecovery",name:"流动资金回收(万元)"},{key:"investmentActivityBuildingInvestment",name:"投资活动建设投资(万元)"},{key:"investmentActivityOperationInvestment",name:"投资活动运营投资(万元)"},{key:"investmentActivityWorkingCapital",name:"投资活动流动资金(万元)"},{key:"investmentActivityOtherOutflow",name:"投资活动其他流出(万元)"},{key:"investmentActivityCashOutflow",name:"投资活动现金流出(万元)"},{key:"investmentActivitiesNetCashFlow",name:"投资活动净现金流量(万元)"},{key:"financingActivityCashInflow",name:"筹资活动现金流入(万元)"},{key:"equityCapitalInvestment",name:"资本金投资(万元)"},{key:"buildingInvestmentLoan",name:"建设投资借款(万元)"},{key:"workingCapitalLoan",name:"流动资金借款(万元)"},{key:"bond",name:"债券(万元)"},{key:"shortTermLoan",name:"短期借款(万元)"},{key:"financingActivityOtherInflow",name:"筹资活动其他流入(万元)"},{key:"interestExpense",name:"利息支出(万元)"},{key:"debtPrincipalRepayment",name:"偿还债务本金(万元)"},{key:"payableProfit",name:"应付利润(万元)"},{key:"financingActivityOtherOutflow",name:"筹资活动其他流出(万元)"},{key:"financingActivityCashOutflow",name:"筹资活动现金流出(万元)"},{key:"financingActivitiesNetCashFlow",name:"筹资活动净现金流量(万元)"},{key:"netCashFlow",name:"净现金流量(万元)"},{key:"accumulatedSurplusFunds",name:"累计盈余资金(万元)"}],balanceSheet:[{key:"totalCurrentAssets",name:"流动资产合计(万元)"},{key:"accumulatedSurplusFunds",name:"累计盈余资金(万元)"},{key:"currentAssets",name:"流动资产(万元)"},{key:"constructionInProgress",name:"在建工程(万元)"},{key:"netFixedAssets",name:"固定资产净值(万元)"},{key:"netIntangibleAndOtherAssets",name:"无形资产及其他资产净值(万元)"},{key:"vatDeductibleAssets",name:"增值税可抵扣资产(万元)"},{key:"assets",name:"资产总计(万元)"},{key:"totalCurrentLiabilities",name:"流动负债合计(万元)"},{key:"currentYearShortTermLoan",name:"本年短期借款(万元)"},{key:"otherLiabilities",name:"其他负债(万元)"},{key:"constructionInvestmentLoan",name:"建设投资借款(万元)"},{key:"workingCapitalLoan",name:"流动资金借款(万元)"},{key:"totalLiabilities",name:"负债合计(万元)"},{key:"ownersEquity",name:"所有者权益(万元)"},{key:"capital",name:"实收资本(万元)"},{key:"capitalReserve",name:"资本公积(万元)"},{key:"accumulatedSurplusReserves",name:"累计盈余公积(万元)"},{key:"accumulatedUndistributedProfits",name:"累计未分配利润(万元)"},{key:"totalLiabilitiesAndEquity",name:"负债和所有者权益总计(万元)"},{key:"assetLiabilityRatio",name:"资产负债率(%)",isPercentage:!0}]},pe=(e,i,y=25)=>e?i.map((a,r)=>{const m={key:r+1,indicator:a.name};for(let I=1;I<=y;I++){const h=I-1;let n="--";if(a.isFixed)n=e[a.key]||"--";else if(e[a.key]&&e[a.key][h]!==void 0){const c=e[a.key][h];a.isPercentage?n=o(c)+"%":a.isDecimal?n=t(c):n=l(c)}m[`year${I}`]=n}return m}):[],Ie=(e,i,y)=>e?i.map(a=>{const r=e[a.nameKey],m=a.formatter?a.formatter(r):l(r);return{key:a.key,name:a.name,item:a.name,amount:m,value:m,description:a.description}}):[],w=(e,i,y="financialIndicatorsSummary")=>{const a=(e==null?void 0:e.value)||e;if(!a)return null;if(a[y]&&a[y][i]!==void 0)return a[y][i];const r=["fixedAssetsInvestmentEstimation","projectOverallBudget","financingPlan","investmentPlanAndFundRaising"];for(const m of r)if(a[m]&&a[m][i]!==void 0)return a[m][i];return null},$=[{dataKey:"projectTotalInvestment",label:"总投资",unit:"万元",formatter:l},{dataKey:"totalPowerGenerationProfit",label:"利润总额",unit:"万元",formatter:l},{dataKey:"projectInvestmentFIRR_afterTax",label:"项目投资财务内部收益率(税后)",unit:"%",formatter:o},{dataKey:"lcoh_ori",label:"LCOH",unit:"元/Nm³",formatter:t}],q=[{title:"投资",items:[{label:"项目总投资",dataKey:"projectTotalInvestment",unit:"万元",formatter:l},{label:"固定资产静态投资",dataKey:"fixedAssetsStaticInvestment",unit:"万元",formatter:l},{label:"建设期投资",dataKey:"constructionPeriodInterest",unit:"万元",formatter:l},{label:"流动资金",dataKey:"workingCapital",unit:"万元",formatter:l},{label:"资本金",dataKey:"equityCapitalAmount",unit:"万元",formatter:l},{label:"银行贷款",dataKey:"totalBankLoanAmount",unit:"万元",formatter:l},{label:"单瓦静态总投资",dataKey:"staticInvestmentPerWatt",unit:"元/W",formatter:t},{label:"单瓦动态总投资",dataKey:"dynamicInvestmentPerWatt",unit:"元/W",formatter:t}]},{title:"成本与收入",items:[{label:"总营业收入",dataKey:"totalSalesRevenue",unit:"万元",formatter:l},{label:"总成本费用",dataKey:"totalCostExpense",unit:"万元",formatter:l},{label:"年平均运营成本",dataKey:"averageAnnualOperatingCost",unit:"万元",formatter:l},{label:"发电利润总额",dataKey:"totalPowerGenerationProfit",unit:"万元",formatter:l},{label:"资本金净利润率ROE",dataKey:"returnOnEquity",unit:"%",formatter:o},{label:"总投资收益率ROI",dataKey:"returnOnInvestment",unit:"%",formatter:o},{label:"资产负债率",dataKey:"assetLiabilityRatio",unit:"%",formatter:o}]},{title:"收益率与回收期",items:[{label:"项目投资财务内部收益率(税前)",dataKey:"projectInvestmentFIRR_beforeTax",unit:"%",formatter:o},{label:"项目投资财务内部收益率(税后)",dataKey:"projectInvestmentFIRR_afterTax",unit:"%",formatter:o},{label:"资本金财务内部收益率(税前)",dataKey:"equityCapitalFIRR_beforeTax",unit:"%",formatter:o},{label:"资本金财务内部收益率(税后)",dataKey:"equityCapitalFIRR_afterTax",unit:"%",formatter:o},{label:"项目投资财务净现值(税前)",dataKey:"projectInvestmentNPV_beforeTax",unit:"万元",formatter:l},{label:"资本金财务净现值(税后)",dataKey:"equityCapitalNPV_afterTax",unit:"万元",formatter:l},{label:"静态投资回收期",dataKey:"paybackPeriodAfterTax_static",unit:"年",formatter:t},{label:"动态投资回收期",dataKey:"paybackPeriodAfterTax_dynamic",unit:"年",formatter:t}]},{title:"发电与制氢指标",items:[{label:"装机规模",dataKey:"installedCapacity",unit:"MW",formatter:t},{label:"年均发电量",dataKey:"averageAnnualGeneration",unit:"万kW·h",formatter:t},{label:"上网总结算电量",dataKey:"totalGridElectricity",unit:"万kW·h",formatter:t},{label:"年均上网结算电量",dataKey:"averageAnnualGridElectricity",unit:"万kW·h",formatter:t},{label:"平准化度电成本LCOE",dataKey:"lcoe_ori",unit:"元/kWh",formatter:t},{label:"平准化制氢成本LCOH",dataKey:"lcoh_ori",unit:"元/Nm³",formatter:t}]}],B=e=>(P("data-v-8b58393c"),e=e(),T(),e),V={class:"key-indicators"},G=B(()=>s("h2",{class:"section-title"},"关键指标",-1)),H={class:"indicators-grid"},M={class:"indicator-value"},Y={class:"indicator-label"},z={class:"summary-cards"},U={class:"card-title"},J={class:"card-content"},Q={class:"label"},X={class:"value"},Z={class:"unit"},ee={__name:"KeyIndicators",props:{resultData:{type:Object,default:()=>null}},setup(e){return(i,y)=>(u(),k("div",V,[G,s("div",H,[(u(!0),k(x,null,b(C($),a=>(u(),k("div",{key:a.dataKey,class:"indicator-card"},[s("div",M,p(a.formatter(C(w)(e.resultData,a.dataKey)))+p(a.unit),1),s("div",Y,p(a.label),1)]))),128))]),s("div",z,[(u(!0),k(x,null,b(C(q),a=>(u(),k("div",{key:a.title,class:"summary-card"},[s("h3",U,p(a.title),1),s("div",J,[(u(!0),k(x,null,b(a.items,r=>(u(),k("div",{key:r.dataKey,class:"data-item"},[s("span",Q,p(r.label),1),s("span",X,p(r.formatter(C(w)(e.resultData,r.dataKey))),1),s("span",Z,p(r.unit),1)]))),128))])]))),128))])]))}},ve=_(ee,[["__scopeId","data-v-8b58393c"]]),R=e=>(P("data-v-8791dec1"),e=e(),T(),e),te={class:"sensitivity-analysis"},ae=R(()=>s("h2",{class:"section-title"},"敏感性分析",-1)),ne={key:0,class:"analysis-content"},ie={class:"indicator-title"},re={class:"analysis-table-wrapper"},oe={key:1,class:"empty-state"},me=R(()=>s("div",{class:"empty-text"},"暂无敏感性分析数据",-1)),se=[me],ye={__name:"SensitivityAnalysis",props:{sensitivityData:{type:Array,default:()=>[]}},setup(e){const i={investCost:"静态投资成本",gridHydrogenElectricityPriceNoTax:"下网电价(不含税)",gridElectricityPriceNoTax:"上网电价(不含税)",baseLoanRate:"借款利率",financingRatioBase:"贷款比例",hydrogenPriceNoTax:"氢气售价(不含税)"},y=n=>i[n]||n,a=(n,c)=>c===0?`${(n*100).toFixed(1)}%`:c===1?n>=0?`+${n}`:`${n}`:n.toString(),r=n=>`${(n*100).toFixed(2)}%`,m=(n,c)=>{if(n==null||n==="-")return"-";let d=Number(n);if(isNaN(d))return"-";if(["baseLoanRate","financingRatioBase"].includes(c)&&(d=d*100),d===0)return"0";const K=Math.abs(d);let g;return K>=1e3?g=0:K>=100?g=1:K>=10?g=2:K>=1?g=3:g=4,d.toFixed(g)},I=n=>{var f;return[{title:"变化幅度",dataIndex:"changeValue",key:"changeValue",width:120,align:"center"},{title:`值(${((f={investCost:{unit:"万元"},gridElectricityPriceNoTax:{unit:"元/kWh"},gridHydrogenElectricityPriceNoTax:{unit:"元/kWh"},baseLoanRate:{unit:"%"},financingRatioBase:{unit:"%"},hydrogenPriceNoTax:{unit:"元/kg"}}[n.key])==null?void 0:f.unit)||""})`,dataIndex:"cur_value",key:"cur_value",width:120,align:"center"},{title:"项目投资财务内部收益率(税后)",dataIndex:"projectInvestmentFIRR_afterTax",key:"projectInvestmentFIRR_afterTax",width:200,align:"center"},{title:"资本金财务内部收益率(税后)",dataIndex:"equityCapitalFIRR_afterTax",key:"equityCapitalFIRR_afterTax",width:200,align:"center"}]},h=n=>{if(!n.value||!n.result)return[];const c=Math.min(n.value.length,n.result.length);return n.value.slice(0,c).map((d,f)=>{const v=n.result[f]||{};return{key:f,changeValue:a(d,n.type),cur_value:m(v.cur_value,n.key),projectInvestmentFIRR_afterTax:r(v.projectInvestmentFIRR_afterTax||0),equityCapitalFIRR_afterTax:r(v.equityCapitalFIRR_afterTax||0)}})};return(n,c)=>{const d=F("a-table");return u(),k("div",te,[ae,e.sensitivityData&&e.sensitivityData.length>0?(u(),k("div",ne,[(u(!0),k(x,null,b(e.sensitivityData,(f,v)=>(u(),k("div",{key:f.key,class:"indicator-section"},[s("h3",ie,p(y(f.key)),1),s("div",re,[L(d,{columns:I(f),"data-source":h(f),pagination:!1,size:"small",bordered:"",class:"sensitivity-table"},null,8,["columns","data-source"])])]))),128))])):(u(),k("div",oe,se))])}}},he=_(ye,[["__scopeId","data-v-8791dec1"]]);export{ve as K,ce as P,he as S,pe as a,Ie as b,de as c,t as d,l as e,o as f,ke as g,ue as t,fe as y};
