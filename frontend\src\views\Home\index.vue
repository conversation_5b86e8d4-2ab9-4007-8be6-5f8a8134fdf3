<template>
  <div class="body_wrap">
      <div class="part_wrap">
        <div class="title1">
          {{ $t('home.title') }}
        </div>
        <a-spin :spinning="loading">
          <div class="content_wrap" v-if="tableData?.length">
            <div class="box_wrap box_recent">
              <div class="b_title">{{ $t('home.recentProject.title') }}</div>
              <div class="b_body">
                <div class="b_name">{{ tableData[0]?.name }}</div>
                <!-- <div class="b_k_v_item">
                  <div class="key">客户需求：</div>
                  <div class="val">{{ tableData[0].desc }}</div>
                </div> -->
                <div class="b_k_v_item">
                  <div class="key">{{ $t('home.recentProject.customerName') }}</div>
                  <div class="val">{{ tableData[0]?.customer }}</div>
                </div>
                <div class="b_k_v_item">
                  <div class="key">{{ $t('home.recentProject.projectBackground') }}</div>
                  <div class="val">{{ tableData[0]?.desc }}</div>
                </div>
              </div>
              <div class="see_detail">
                <a type="link"  @click="router.push({
                  name: 'projectDetail',
                  query: {
                    projectId: tableData[0]?.id,
                    type: 'list',
                    solutionId: tableData[0]?.solutions?.[0]?.id
                  },
                })">{{ $t('home.recentProject.viewDetail') }}</a>
              </div>
            </div>
            <div class="box_wrap box_mine">
              <div class="b_title">{{ $t('home.myProject.title') }}</div>
              <div class="b_body">
                <a-table
                  :columns="columns" :data-source="tableData.slice(0,5)" :pagination="false"
                  size="small"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'tags'">
                      <span>
                        <a-tag
                          v-for="tag in record.tags"
                          :key="tag"
                          :color="tag === 'loser' ? 'volcano' : tag?.length > 5 ? 'geekblue' : 'green'"
                        >
                          {{ tag.toUpperCase() }}
                        </a-tag>
                      </span>
                    </template>
                    <template v-else-if="column.key === 'action'">
                      <span>
                        <a-button type="link" size="small">{{ $t('home.myProject.actions.detail') }}</a-button>
                        <a-button type="link" size="small">{{ $t('home.myProject.actions.pauseStart') }}</a-button>
                        <a-button type="link" size="small">{{ $t('home.myProject.actions.delete') }}</a-button>
                        <a-button type="link" size="small">{{ $t('home.myProject.actions.config') }}</a-button>
                      </span>
                    </template>
                  </template>
                </a-table>
              </div>
              <div class="see_detail">
                <a type="link"  @click="router.push({ name: 'projectList' })">{{ $t('home.myProject.viewDetail') }}</a>
              </div>
            </div>
            <div class="box_wrap box_stat">
              <div class="b_title">{{ $t('home.projectStats.title') }}</div>
              <div class="b_body">
                <BarChart :data="chartData" />
              </div>
            </div>
          </div>
          <a-empty
            v-if="!tableData?.length"
          >
            <template #description>
              <span>
                {{ $t('home.empty.noData') }}
              </span>
            </template>
            <a-button type="primary" @click="router.push({name:'createProject'})">{{ $t('home.empty.createNewProject') }}</a-button>
          </a-empty>
        </a-spin>
      </div>
      <div class="part_wrap">
        <div class="title1">
          {{ $t('home.guideTitle') }}
        </div>
        <div class="step_wrap">
          <div class="s_w_title">{{ $t('home.capacityCalculation.title') }} </div>
          <div class="step_item_wrap">
            <div class="step_item">
              <div class="number_wrap">01</div>
              <div class="line_wrap"></div>
            </div>
            <div class="step_item">
              <div class="number_wrap">02</div>
              <div class="line_wrap"></div>
            </div>
            <div class="step_item">
              <div class="number_wrap">03</div>
              <div class="line_wrap"></div>
            </div>
          </div>
          <div class="step_content_wrap">
            <div class="s_c_item">
              <div class="s_c_title">{{ $t('home.capacityCalculation.steps.step1.title') }}</div>
              <div class="s_c_desc">{{ $t('home.capacityCalculation.steps.step1.description') }}</div>
              <img class="s_c_img" :src="baseConfig.step1" height="80%"  />
            </div>
            <div class="s_c_item">
              <div class="s_c_title">{{ $t('home.capacityCalculation.steps.step2.title') }}</div>
              <div class="s_c_desc">{{ $t('home.capacityCalculation.steps.step2.description') }}</div>
              <img class="s_c_img" :src="baseConfig.step2" height="80%"  />
            </div>
            <div class="s_c_item">
              <div class="s_c_title">{{ $t('home.capacityCalculation.steps.step3.title') }}</div>
              <div class="s_c_desc">{{ $t('home.capacityCalculation.steps.step3.description') }}</div>
              <img class="s_c_img" :src="baseConfig.step3" height="80%"  />
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import BarChart from '@/components/BarChart/index.vue'
import { getProjects, getSolutionTypes } from '@/api/project'
import { useRouter } from 'vue-router'
import { baseConfig } from '@/config/baseConfig'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const tableData = ref([])
const loading = ref(false)
const chartData = ref({})

// 使用计算属性来实现动态国际化
const columns = computed(() => [
  {
    title: t('home.myProject.columns.solutionName'),
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: t('home.myProject.columns.customerName'),
    dataIndex: 'customer',
    key: 'customer',
  }
])


const initData = async () => {
  loading.value = true
  const { code, data: { total, result }, msg } = await getProjects({ pageNumber: 0, pageSize: 5 })
  const { data: pieData } = await getSolutionTypes()
  // const pieData = {
  //   1: { number: 0 },
  //   2: { number: 2 },
  //   3: { number: 3 },
  //   4: { number: 5 },
  //   5: { number: 7 },
  //   6: { number: 8 },
  //   7: { number: 3 },
  //   8: { number: 10 },
  // }
  // console.log('pieData:', pieData)
  const dataResult = []
  for (let key in pieData) {
    dataResult[parseInt(key) - 1] = pieData[key].number
  }
  chartData.value = {
    series: [
      {
        // data: [12, 20, 15, 8, 7, 11, 13, 20],
        data: dataResult,
        type: 'bar',
        barWidth: '50%',
        color: baseConfig.homeBarChartColor
      }
    ]
  }
  tableData.value = result // TODO:
  // tableData.value = []

  loading.value = false
  console.log('r:', tableData)
}

onMounted(() => {
  initData()
})
</script>

<style lang="less" scoped>
@import '@/style/base.less';

.body_wrap {
  padding: 10px 20px;
}
.part_wrap {
  margin-bottom: 30px;
}
.content_wrap {
  display: flex;
  .box_wrap {
    padding: 20px;
    background-color: #fff;
    position: relative;
    .b_title {
      font-size: 14px;
      font-weight: bold;
    }
    .b_body {
      margin-top: 20px;
      .b_name {
        background: @baseColor;
        color: #fff;
        display: inline-block;
        padding: 3px 15px;
        border-radius: 3px;
      }
      .b_k_v_item {
        display: flex;
        align-items: flex-start;
        margin-top: 10px;
        .key {
    
          width: 80px;
          margin-right: 5px;
          flex-shrink: 0
        }
        .val {
        }
      }
    }
  }
  .box_recent {
    width: 25%;
  }
  .box_mine {
    margin: 0 3%;
    width: 25%;
  }
  .box_stat {
    width: 47%;
    .b_body {
      width: 100%;
      height: 260px;
    }
  }
  .see_detail {
    position: absolute;
    bottom: 15px;
    right: 15px;
    a {
      color: @baseColor;
    }
  }
}
.step_wrap {
  width: 100%;
  background-color: #fff;
  padding: 20px;
  .s_w_title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 15px;
  }
  .step_item_wrap {
    margin-top: 20px;
    display: flex;
    align-items: center;
    // position: relative;
  }
  .step_item {
    width: 33%;
    display: flex;
    align-items: center;
  }
  .number_wrap {
    // position: relative;
    left: 0;
    background: @baseColor;
    width: 56px;
    height: 29px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    color: #fff;
  }
  .line_wrap {
    // position: relative;
    height: 2px;
    width: 100%;
    background: @baseColor;
    top: 50%;
  }
  .step_content_wrap {
    .s_c_title {
      font-size: 14px;
      font-weight: bold;
    }
    .s_c_item {
      width: 33.33%;
      padding-right: 3%;
      padding-top: 15px;
      .s_c_desc {
        font-size: 13px;
        margin: 10px 0;
        line-height: 1.3;
      }
    }
    .s_c_img {
      width: 100%;
    }
    display: flex;

  }
}

</style>
