import{_ as Y,r as b,e as W,f as k,b as q,c as H,h as s,w as f,F as O,l as z,E as U,G as J,g as T,d as Q,z as V,o as X,v as M,i as Z,n as N}from"./index-CtrMLNJH.js";import{b as ee,a as ae}from"./index-DHzTpAGQ.js";import{g as te,t as oe,a as se,y as ne,b as le,c as re,P as ie,K as ce,S as ue}from"./SensitivityAnalysis-B92Es7lU.js";import{s as de,d as ye}from"./index-UGdhnffj.js";import{u as $,w as pe}from"./xlsx-D5JNrnKm.js";const fe=K=>(U("data-v-a52455b5"),K=K(),J(),K),me={class:"detail-indicators"},be=fe(()=>T("div",{class:"section-header"},[T("h2",{class:"section-title"},"详情指标")],-1)),_e={__name:"TabTables",props:{resultData:{type:Object,default:()=>null}},setup(K,{expose:j}){const n=K,u=b("2"),_=W(()=>{var t,r;return((r=(t=n.resultData)==null?void 0:t.financialIndicatorsSummary)==null?void 0:r.operatingYears)||25}),g=W(()=>_.value+1),d=[{key:"2",tab:"固定资产投资估算",type:"basic",dataKey:"fixedAssetsInvestmentEstimation",configKey:"fixedAssets"},{key:"3",tab:"工程总概算",type:"basic",dataKey:"projectOverallBudget",configKey:"projectBudget"},{key:"4",tab:"融资计划",type:"basic",dataKey:"financingPlan",configKey:"financing"},{key:"5",tab:"投资计划与资金筹措",type:"basic",dataKey:"investmentPlanAndFundRaising",configKey:"investmentPlan"},{key:"6",tab:"年度制氢及上网电量",type:"yearly",dataKey:"annualHydrogenAndGridPower",configKey:"annualHydrogenAndGridPower"},{key:"7",tab:"还本付息计算",type:"yearly",dataKey:"loanRepaymentSchedule",configKey:"loanRepaymentSchedule"},{key:"8",tab:"总成本费用",type:"yearly",dataKey:"totalCostAndExpenses",configKey:"totalCostAndExpenses"},{key:"9",tab:"利润和利润分配",type:"yearly",dataKey:"profitAndProfitDistribution",configKey:"profitAndProfitDistribution"},{key:"10",tab:"项目投资现金流量",type:"yearly",dataKey:"projectInvestmentCashFlow",configKey:"projectInvestmentCashFlow"},{key:"11",tab:"资本金财务现金流量",type:"yearly",dataKey:"equityCapitalCashFlow",configKey:"equityCapitalCashFlow"},{key:"12",tab:"财务计划现金流量",type:"yearly",dataKey:"financialPlanCashFlow",configKey:"financialPlanCashFlow"},{key:"13",tab:"资产负债",type:"yearly",dataKey:"balanceSheet",configKey:"balanceSheet"},{key:"14",tab:"财务指标汇总",type:"basic",dataKey:"financialIndicatorsSummary",configKey:"financialSummary"}],C=t=>t.type==="yearly"?te(g.value):oe[t.configKey],w=t=>{var r;return(r=n.resultData)!=null&&r[t.dataKey]?t.type==="yearly"?se(n.resultData[t.dataKey],ne[t.configKey],g.value):le(n.resultData[t.dataKey],re[t.configKey]):[]};return j({tableConfigs:d,getTableColumns:C,getTableData:w}),(t,r)=>{const A=k("a-table"),B=k("a-tab-pane"),L=k("a-tabs");return q(),H("div",me,[be,s(L,{activeKey:u.value,"onUpdate:activeKey":r[0]||(r[0]=y=>u.value=y),class:"detail-tabs"},{default:f(()=>[(q(),H(O,null,z(d,y=>s(B,{key:y.key,tab:y.tab},{default:f(()=>[s(A,{columns:C(y),"data-source":w(y),pagination:!1,size:"small",bordered:"",scroll:y.type==="yearly"?{x:"max-content"}:void 0},null,8,["columns","data-source","scroll"])]),_:2},1032,["tab"])),64))]),_:1},8,["activeKey"])])}}},ge=Y(_e,[["__scopeId","data-v-a52455b5"]]),ve={class:"economic-detail"},he={class:"header-section"},Ie={class:"title-wrapper"},xe={class:"header-actions"},ke={__name:"index",setup(K){const j=Q(),n=V(),u=b(null),_=b(null),g=b({}),d=b(!1),C=b("数据加载中..."),w=b(!1),t=b(null),r=async()=>{try{d.value=!0;const{code:o,data:a,msg:i}=await ee({projectId:n.params.projectId,solutionId:n.params.solutionId});console.log("接口返回数据:",a),console.log("lcoh:",a.resultTables.financialIndicatorsSummary.lcoh),console.log("Excel原 lcoh_ori:",a.resultTables.financialIndicatorsSummary.lcoh_ori),console.log("lcoe:",a.resultTables.financialIndicatorsSummary.lcoe),console.log("Excel原 lcoe_ori:",a.resultTables.financialIndicatorsSummary.lcoe_ori),console.log("敏感性分析数据:",a.resultAnalysis),o===0&&(u.value=a.resultTables,_.value=a.resultAnalysis||null)}catch(o){console.error("获取数据失败:",o),u.value=null}finally{d.value=!1}},A=async()=>{try{const{code:o,data:a,msg:i}=await ae({projectId:n.params.projectId,solutionId:n.params.solutionId});o===0&&(g.value=a)}catch(o){console.error("获取方案信息失败:",o)}},B=()=>{j.push({path:"/report-detail",query:{projectId:n.params.projectId,solutionId:n.params.solutionId}})},L=async()=>{var a,i;d.value=!0,C.value="报告生成中...";const o=await de(`${location.origin}/report-detail?projectId=${n.params.projectId}&solutionId=${n.params.solutionId}&type=1`);await ye(`/api/v1/cecp/result/export?encodedUrl=${o}`,`${(i=(a=g.value)==null?void 0:a.project)==null?void 0:i.name}.pdf`),setTimeout(()=>{d.value=!1},6500)},y=async()=>{if(!u.value){M.warning("暂无数据可导出");return}try{w.value=!0,await G(),M.success("数据导出成功")}catch(o){console.error("导出失败:",o),M.error("导出失败，请重试")}finally{w.value=!1}},G=async()=>{var i,F;const o=$.book_new();if(t.value&&u.value){const{tableConfigs:v,getTableColumns:S,getTableData:e}=t.value;v.forEach(h=>{const l=S(h),D=e(h);if(D&&D.length>0){const E=l.map(x=>x.title),m=D.map(x=>l.map(P=>{const p=x[P.dataIndex];return p==="--"||p===null||p===void 0?"":p})),c=[E,...m],R=$.aoa_to_sheet(c),I=l.map(x=>({wch:Math.max(x.title.length,15)}));R["!cols"]=I,$.book_append_sheet(o,R,h.tab)}})}if(_.value&&_.value.length>0){const v={investCost:"静态投资成本",gridHydrogenElectricityPriceNoTax:"下网电价(不含税)",gridElectricityPriceNoTax:"上网电价(不含税)",baseLoanRate:"借款利率",financingRatioBase:"贷款比例",hydrogenPriceNoTax:"氢气售价(不含税)"},S=(e,h)=>{if(e==null||e==="-")return"-";let l=Number(e);if(isNaN(l))return"-";if(["baseLoanRate","financingRatioBase"].includes(h)&&(l=l*100),l===0)return"0";const m=Math.abs(l);let c;return m>=1e3?c=0:m>=100?c=1:m>=10?c=2:m>=1?c=3:c=4,l.toFixed(c)};_.value.forEach(e=>{const h=v[e.key]||e.key;if(e.value&&e.result&&e.value.length>0){const E=["变化幅度",`值(${{investCost:"万元",gridElectricityPriceNoTax:"元/kWh",gridHydrogenElectricityPriceNoTax:"元/kWh",baseLoanRate:"%",financingRatioBase:"%",hydrogenPriceNoTax:"元/kg"}[e.key]||""})`,"项目投资财务内部收益率(税后)","资本金财务内部收益率(税后)"],m=e.value.map((I,x)=>{const P=e.result[x]||{};let p;return e.type===0?p=`${(I*100).toFixed(1)}%`:e.type===1?p=I>=0?`+${I}`:`${I}`:p=I.toString(),[p,S(P.cur_value,e.key),`${((P.projectInvestmentFIRR_afterTax||0)*100).toFixed(2)}%`,`${((P.equityCapitalFIRR_afterTax||0)*100).toFixed(2)}%`]}),c=[E,...m],R=$.aoa_to_sheet(c);R["!cols"]=[{wch:15},{wch:20},{wch:30},{wch:30}],$.book_append_sheet(o,R,`敏感性分析-${h}`)}})}const a=`经济分析数据_${((F=(i=g.value)==null?void 0:i.project)==null?void 0:F.name)||"未命名项目"}_${new Date().toISOString().slice(0,10)}.xlsx`;pe(o,a)};return X(async()=>{await Promise.all([r(),A()])}),(o,a)=>{const i=k("a-breadcrumb-item"),F=k("a-breadcrumb"),v=k("a-button"),S=k("a-spin");return q(),H("div",ve,[T("div",he,[T("div",Ie,[s(F,null,{default:f(()=>[s(i,null,{default:f(()=>[T("a",{onClick:a[0]||(a[0]=e=>Z(j).back())}," < 经济分析")]),_:1}),s(i,null,{default:f(()=>[N("分析结果")]),_:1})]),_:1})]),T("div",xe,[s(v,{class:"action-btn",onClick:L,loading:d.value},{default:f(()=>[N("下载报告")]),_:1},8,["loading"]),s(v,{class:"action-btn",onClick:y,loading:w.value},{default:f(()=>[N("数据导出excel")]),_:1},8,["loading"]),s(v,{type:"primary",class:"action-btn",onClick:B},{default:f(()=>[N("查看报告")]),_:1})])]),s(S,{spinning:d.value,tip:C.value},{default:f(()=>[s(ie,{"solution-info":g.value},null,8,["solution-info"]),s(ce,{"result-data":u.value},null,8,["result-data"]),s(ge,{ref_key:"tabTablesRef",ref:t,"result-data":u.value},null,8,["result-data"]),s(ue,{"sensitivity-data":_.value},null,8,["sensitivity-data"])]),_:1},8,["spinning","tip"])])}}},De=Y(ke,[["__scopeId","data-v-14cafb82"]]);export{De as default};
