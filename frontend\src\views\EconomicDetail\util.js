// 格式化数字
export const formatNumber = (num) => {
  if (num === null || num === undefined || isNaN(num)) return '--'
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num)
}

// 格式化百分比
export const formatPercentage = (num) => {
  if (num === null || num === undefined || isNaN(num)) return '--'
  if (num == Infinity) return Infinity

  return (num * 100).toFixed(2)
}

// 格式化小数
export const formatDecimal = (num) => {
  if (num === null || num === undefined || isNaN(num)) return '--'
  if (num == Infinity) return Infinity
  return num.toFixed(3)
}

// 表格列配置
export const tableColumns = {
  // 基础表格列配置
  keyIndicator: [
    { title: '指标名称', dataIndex: 'name', key: 'name', width: 250 },
    { title: '数值', dataIndex: 'value', key: 'value', width: 150 },
    { title: '说明', dataIndex: 'description', key: 'description' }
  ],
  
  fixedAssets: [
    { title: '项目', dataIndex: 'item', key: 'item', width: 250 },
    { title: '投资额', dataIndex: 'amount', key: 'amount', width: 150 }
  ],
  
  projectBudget: [
    { title: '项目', dataIndex: 'item', key: 'item', width: 250 },
    { title: '金额', dataIndex: 'amount', key: 'amount', width: 150 }
  ],
  
  financing: [
    { title: '项目', dataIndex: 'item', key: 'item', width: 250 },
    { title: '金额', dataIndex: 'amount', key: 'amount', width: 150 }
  ],
  
  investmentPlan: [
    { title: '项目', dataIndex: 'item', key: 'item', width: 250 },
    { title: '金额', dataIndex: 'amount', key: 'amount', width: 150 }
  ],
  
  financialSummary: [
    { title: '指标名称', dataIndex: 'name', key: 'name', width: 250 },
    { title: '数值', dataIndex: 'value', key: 'value', width: 150 }
  ]
}

// 动态年份列配置
export const generateYearColumns = (yearCount = 25) => {
  const columns = [
    { title: '指标', dataIndex: 'indicator', key: 'indicator', fixed: 'left', width: 200 }
  ]
  
  // 动态添加年份列
  for (let i = 1; i <= yearCount; i++) {
    columns.push({
      title: `第${i}年`,
      dataIndex: `year${i}`,
      key: `year${i}`,
      width: 100
    })
  }
  
  return columns
}

// 表格数据配置
export const tableDataConfigs = {
  // 关键指标表数据配置
  keyIndicators: [
    { key: '1', nameKey: 'loanRate', name: '借款利率', formatter: formatPercentage },
    { key: '2', nameKey: 'totalEducationSurchargeRate', name: '总教育费附加税率', formatter: formatPercentage }
  ],

  // 固定资产投资估算表数据配置
  fixedAssets: [
    { key: '7', nameKey: 'projectTotalInvestment', name: '项目总投资(万元)' },
    { key: '1', nameKey: 'mechanicalAndElectricalEquipment', name: '机电设备及安装工程(万元)' },
    { key: '2', nameKey: 'constructionWorks', name: '建筑工程(万元)' },
    { key: '3', nameKey: 'otherExpenses', name: '其他费用(万元)' },
    { key: '4', nameKey: 'fixedAssetsStaticInvestment', name: '固定资产静态投资(万元)' },
    { key: '5', nameKey: 'workingCapital', name: '流动资金(万元)' },
    { key: '6', nameKey: 'constructionPeriodInterest', name: '建设期利息(万元)' },
    // { key: '8', nameKey: 'fixedPowerAssetsNoTax', name: '固定发电资产不含税(万元)' },
    // { key: '9', nameKey: 'fixedHydrogenAssetsNoTax', name: '固定制氢资产不含税(万元)' },
    { key: '10', nameKey: 'fixedAssetsNoTax', name: '固定资产不含税(万元)' },
    // { key: '11', nameKey: 'fixedPowerAssetsWithTax', name: '固定发电资产含税(万元)' },
    // { key: '12', nameKey: 'fixedHydrogenAssetsWithTax', name: '固定制氢资产含税(万元)' },
    { key: '13', nameKey: 'fixedAssetsWithTax', name: '固定资产含税(万元)' },
    { key: '14', nameKey: 'equipmentAndInstallationNoTax', name: '设备及安装不含税(万元)' },
    // { key: '15', nameKey: 'powerEquipmentAndInstallationNoTax', name: '发电设备及安装不含税(万元)' },
    // { key: '16', nameKey: 'powerEquipmentAndInstallationWithTax', name: '发电设备及安装含税(万元)' },
    // { key: '17', nameKey: 'hydrogenEquipmentAndInstallationNoTax', name: '制氢设备及安装不含税(万元)' },
    // { key: '18', nameKey: 'hydrogenEquipmentAndInstallationWithTax', name: '制氢设备及安装含税(万元)' },
    { key: '19', nameKey: 'equipmentPurchaseCost', name: '设备购置费(万元)' },
    { key: '20', nameKey: 'installationCost', name: '安装工程费(万元)' },
    // { key: '21', nameKey: 'windTurbineEquipmentAndInstallationNoTax', name: '风机设备及安装不含税(万元)' },
    // { key: '22', nameKey: 'photovoltaicEquipmentAndInstallationNoTax', name: '光伏设备及安装不含税(万元)' },
    // { key: '23', nameKey: 'energyStorageEquipmentAndInstallationNoTax', name: '储能设备及安装不含税(万元)' },
    // { key: '24', nameKey: 'electrolyzerEquipmentAndInstallationNoTax', name: '电解槽设备及安装不含税(万元)' },
    // { key: '25', nameKey: 'hydrogenStorageEquipmentAndInstallationNoTax', name: '储氢设备及安装不含税(万元)' },
    // { key: '26', nameKey: 'fixedWindTurbineAssetsNoTax', name: '固定风机资产不含税(万元)' },
    // { key: '27', nameKey: 'fixedPhotovoltaicAssetsNoTax', name: '固定光伏资产不含税(万元)' },
    // { key: '28', nameKey: 'fixedEnergyStorageAssetsNoTax', name: '固定储能资产不含税(万元)' },
    // { key: '29', nameKey: 'fixedElectrolyzerAssetsNoTax', name: '固定电解槽资产不含税(万元)' },
    // { key: '30', nameKey: 'fixedHydrogenStorageAssetsNoTax', name: '固定储氢资产不含税(万元)' },
    // { key: '31', nameKey: 'windTurbineEquipmentAndInstallationWithTax', name: '风机设备及安装含税(万元)' },
    // { key: '32', nameKey: 'photovoltaicEquipmentAndInstallationWithTax', name: '光伏设备及安装含税(万元)' },
    // { key: '33', nameKey: 'energyStorageEquipmentAndInstallationWithTax', name: '储能设备及安装含税(万元)' },
    // { key: '34', nameKey: 'electrolyzerEquipmentAndInstallationWithTax', name: '电解槽设备及安装含税(万元)' },
    // { key: '35', nameKey: 'hydrogenStorageEquipmentAndInstallationWithTax', name: '储氢设备及安装含税(万元)' },
    // { key: '36', nameKey: 'fixedWindTurbineAssetsWithTax', name: '固定风机资产含税(万元)' },
    // { key: '37', nameKey: 'fixedPhotovoltaicAssetsWithTax', name: '固定光伏资产含税(万元)' },
    // { key: '38', nameKey: 'fixedEnergyStorageAssetsWithTax', name: '固定储能资产含税(万元)' },
    // { key: '39', nameKey: 'fixedElectrolyzerAssetsWithTax', name: '固定电解槽资产含税(万元)' },
    // { key: '40', nameKey: 'fixedHydrogenStorageAssetsWithTax', name: '固定储氢资产含税(万元)' }
  ],

  // 工程总概算表数据配置
  projectBudget: [
    { key: '1', nameKey: 'mechanicalAndElectricalEquipment', name: '机电设备及安装工程(万元)' },
    { key: '2', nameKey: 'constructionWorks', name: '建筑工程(万元)' },
    { key: '3', nameKey: 'otherExpenses', name: '其他费用(万元)' },
    { key: '4', nameKey: 'staticInvestment', name: '静态投资(万元)' },
    { key: '5', nameKey: 'constructionPeriodSubsidy', name: '建设期补助(万元)' },
    { key: '6', nameKey: 'constructionInvestment', name: '建设投资(万元)' },
    { key: '7', nameKey: 'constructionPeriodInterest', name: '建设期利息(万元)' },
    { key: '8', nameKey: 'totalProjectInvestment', name: '工程总投资(万元)' },
    { key: '9', nameKey: 'dynamicInvestmentPerWatt', name: '单瓦动态总投资(元/W)' },
    { key: '10', nameKey: 'staticInvestmentPerWatt', name: '单瓦静态总投资(元/W)' }
  ],

  // 融资计划表数据配置
  financing: [
    { key: '1', nameKey: 'projectTotalInvestment', name: '项目总投资(万元)' },
    { key: '2', nameKey: 'bankLoan', name: '银行借款(万元)' },
    { key: '3', nameKey: 'equityCapital', name: '资本金(万元)' }
  ],

  // 投资计划与资金筹措表数据配置
  investmentPlan: [
    { key: '1', nameKey: 'projectTotalInvestment', name: '项目总投资(万元)' },
    { key: '2', nameKey: 'fixedAssetsStaticInvestment', name: '固定资产静态投资(万元)' },
    { key: '3', nameKey: 'foreignInvestment', name: '外商投资(万元)' },
    { key: '4', nameKey: 'domesticInvestment', name: '国内投资(万元)' },
    { key: '5', nameKey: 'constructionPeriodInterest', name: '建设期利息(万元)' },
    { key: '6', nameKey: 'foreignInterestDuringConstruction', name: '外商建设期利息(万元)' },
    { key: '7', nameKey: 'domesticInterestDuringConstruction', name: '国内建设期利息(万元)' },
    { key: '8', nameKey: 'workingCapital', name: '流动资金(万元)' },
    { key: '9', nameKey: 'fundRaising', name: '资金筹措(万元)' },
    { key: '10', nameKey: 'equityCapital', name: '资本金(万元)' },
    { key: '11', nameKey: 'bankLoan', name: '银行借款(万元)' },
    { key: '12', nameKey: 'longTermLoan', name: '长期借款(万元)' },
    { key: '13', nameKey: 'domesticLoan', name: '国内借款(万元)' },
    { key: '14', nameKey: 'foreignLoan', name: '外商借款(万元)' },
    { key: '15', nameKey: 'constructionPeriodInterestLoan', name: '建设期利息借款(万元)' },
    { key: '16', nameKey: 'workingCapitalLoan', name: '流动资金借款(万元)' }
  ],

  // 财务指标汇总表数据配置
  financialSummary: [
    // 基本信息
    { key: '1', nameKey: 'installedCapacity', name: '装机规模(MW)' },
    { key: '2', nameKey: 'operatingYears', name: '项目经营期(年)' },
    
    // 发电量指标
    { key: '3', nameKey: 'averageAnnualGeneration', name: '年均发电量(万kW·h)', formatter: formatDecimal },
    { key: '4', nameKey: 'totalGridElectricity', name: '上网总结算电量(万kW·h)', formatter: formatDecimal },
    { key: '5', nameKey: 'averageAnnualGridElectricity', name: '年均上网结算电量(万kW·h)', formatter: formatDecimal },
    { key: '6', nameKey: 'annualEffectiveUtilizationHours', name: '年有效利用小时数(小时)', formatter: formatDecimal },
    
    // 投资指标
    { key: '7', nameKey: 'projectTotalInvestment', name: '项目总投资(万元)', formatter: formatDecimal },
    { key: '8', nameKey: 'fixedAssetsStaticInvestment', name: '固定资产静态投资(万元)', formatter: formatDecimal },
    { key: '9', nameKey: 'constructionPeriodInterest', name: '建设期利息(万元)', formatter: formatDecimal },
    { key: '10', nameKey: 'workingCapital', name: '流动资金(万元)', formatter: formatDecimal },
    { key: '11', nameKey: 'staticInvestmentPerWatt', name: '单瓦静态总投资(元/W)', formatter: formatDecimal },
    { key: '12', nameKey: 'dynamicInvestmentPerWatt', name: '单瓦动态总投资(元/W)', formatter: formatDecimal },
    { key: '13', nameKey: 'investmentCostPerKWh', name: '度电投资成本(元/kWh)', formatter: formatDecimal },
    
    // 资金来源
    { key: '14', nameKey: 'equityCapitalAmount', name: '项目资本金投入金额(万元)', formatter: formatDecimal },
    { key: '15', nameKey: 'totalBankLoanAmount', name: '项目银行贷款总额(万元)', formatter: formatDecimal },
    
    // 经营指标
    { key: '16', nameKey: 'totalSalesRevenue', name: '销售收入总额(万元)', formatter: formatDecimal },
    { key: '17', nameKey: 'totalCostExpense', name: '总成本费用(万元)', formatter: formatDecimal },
    { key: '18', nameKey: 'averageAnnualOperatingCost', name: '年平均运营成本(万元)', formatter: formatDecimal },
    { key: '19', nameKey: 'totalGenerationCostPerUnit', name: '单位发电总成本(元/kWh)', formatter: formatDecimal },
    { key: '20', nameKey: 'operatingCostPerKWh', name: '度电运营成本(元/kWh)', formatter: formatDecimal },
    { key: '21', nameKey: 'totalSalesTaxSurcharges', name: '销售税金附加总额(万元)', formatter: formatDecimal },
    { key: '22', nameKey: 'totalPowerGenerationProfit', name: '发电利润总额(万元)', formatter: formatDecimal },
    { key: '23', nameKey: 'levelizedCostOfElectricity', name: '平准化度电成本LCOE(元/kWh)', formatter: formatDecimal },
    
    // 回收期指标
    { key: '24', nameKey: 'paybackPeriodAfterTax_static', name: '税后投资回收期(静态)(年)', formatter: formatDecimal },
    { key: '25', nameKey: 'paybackPeriodAfterTax_dynamic', name: '税后投资回收期(动态)(年)', formatter: formatDecimal },
    
    // 收益率指标
    { key: '26', nameKey: 'projectInvestmentFIRR_beforeTax', name: '项目投资财务内部收益率_税前(%)', formatter: formatPercentage },
    { key: '27', nameKey: 'projectInvestmentFIRR_afterTax', name: '项目投资财务内部收益率_税后(%)', formatter: formatPercentage },
    { key: '28', nameKey: 'equityCapitalFIRR_beforeTax', name: '资本金财务内部收益率_税前(%)', formatter: formatPercentage },
    { key: '29', nameKey: 'equityCapitalFIRR_afterTax', name: '资本金财务内部收益率_税后(%)', formatter: formatPercentage },
    
    // 其他财务指标
    { key: '30', nameKey: 'hydrogenPrice', name: '氢气价格(元/kg)', formatter: formatDecimal },
    { key: '31', nameKey: 'projectInvestmentNPV_beforeTax', name: '项目投资财务税前净现值(万元)', formatter: formatDecimal },
    { key: '32', nameKey: 'equityCapitalNPV_afterTax', name: '资本金财务税后净现值(万元)', formatter: formatDecimal },
    { key: '33', nameKey: 'returnOnInvestment', name: '总投资收益率ROI(%)', formatter: formatPercentage },
    { key: '34', nameKey: 'investmentTaxRate', name: '投资利税率(%)', formatter: formatPercentage },
    { key: '35', nameKey: 'returnOnEquity', name: '项目资本金净利润率ROE(%)', formatter: formatPercentage },
    { key: '36', nameKey: 'assetLiabilityRatio', name: '资产负债率(%)', formatter: formatPercentage },
    { key: '37', nameKey: 'vatRefund50Percent', name: '增值税即征即退50%(万元)', formatter: formatDecimal },
    { key: '38', nameKey: 'lcoe_ori', name: '平准化度电成本(元/kWh)', formatter: formatDecimal },
    { key: '39', nameKey: 'lcoh_ori', name: '平准化制氢成本(元/Nm³)', formatter: formatDecimal },
    // { key: '40', nameKey: 'lcoh_ori', name: '原始平准化制氢成本(元/Nm³)', formatter: formatDecimal },
    // { key: '41', nameKey: 'lcoh2', name: '调整后平准化制氢成本(元/Nm³)', formatter: formatDecimal }
  ]
}

// 年份横排表格指标配置
export const yearlyTableConfigs = {
  // 年度制氢及上网电量表
  annualHydrogenAndGridPower: [
    { key: 'yearDegradationPercentage', name: '年衰减百分值(%)', isPercentage: true },
    // { key: 'firstYearPowerGenerationHour', name: '发电小时数(小时)', isFixed: true },
    { key: 'componentDegradationEfficiency', name: '组件衰减效率', isDecimal: true },
    { key: 'powerLimitationRate', name: '限电率(%)', isPercentage: true },
    { key: 'powerGenerationRate', name: '发电率(%)', isPercentage: true },
    { key: 'predictedPowerGenerationAfterDegradation', name: '预计发电量(衰减后)(万度)' },
    { key: 'predictedPowerGenerationWithPowerLimitation', name: '预计发电量(衰减+限电)(万度)' },
    { key: 'gridElectricity', name: '上网电量(万度)' },
    { key: 'greenHydrogenElectricity', name: '绿电制氢电量(万度)' },
    { key: 'powerDiscountFactor', name: '发电折现系数', isDecimal: true },
    { key: 'discountedPowerGeneration', name: '发电量折现值(万度)' },
    { key: 'gridHydrogenElectricity', name: '下网制氢电量(万度)' },
    { key: 'hydrogenProduction', name: '制氢量(万公斤)' },
    { key: 'oxygenProduction', name: '制氧量(万公斤)' },
    { key: 'waterConsumption', name: '耗水量(万吨)' },
    { key: 'greenPowerRatioAfterYears', name: '绿电衰减后可用比例(%)', isPercentage: true }
  ],

  // 还本付息计算表
  loanRepaymentSchedule: [
    { key: 'longTermLoan', name: '长期借款(万元)' },
    { key: 'beginningLoanBalance', name: '年初借款余额(万元)' },
    { key: 'annualRepayment', name: '本年还本(万元)' },
    { key: 'annualInterest', name: '本年付息(万元)' },
    { key: 'endingLoanBalance', name: '期末借款余额(万元)' },
    { key: 'currentRepaymentAndInterest', name: '当期还本付息(万元)' },
    { key: 'workingCapitalLoanRepayment', name: '偿还流动资金借款本金(万元)' },
    { key: 'shortTermLoanRepayment', name: '偿还短期借款本金(万元)' },
    { key: 'workingCapitalInterest', name: '流动资金利息(万元)' },
    { key: 'shortTermLoanInterest', name: '短期借款利息(万元)' },
    { key: 'shortTermLoan', name: '短期借款(万元)' },
    { key: 'loanPrincipalRepayment', name: '偿还借款本金(万元)' },
    { key: 'workingCapitalLoan', name: '流动资金借款(万元)' },
    { key: 'loanInterestRepayment', name: '偿还借款利息(万元)' },
    { key: 'totalLoanPayment', name: '借款本息合计(万元)' }
  ],

  // 总成本费用表
  totalCostAndExpenses: [
    // { key: 'incomeTaxRate', name: '所得税率(%)', isPercentage: true },
    { key: 'depreciation', name: '折旧费(万元)' },
    // { key: 'powerDepreciation', name: '发电折旧费(万元)' },
    // { key: 'hydrogenDepreciation', name: '制氢折旧费(万元)' },
    // { key: 'windTurbineDepreciation', name: '风机折旧费(万元)' },
    // { key: 'photovoltaicDepreciation', name: '光伏折旧费(万元)' },
    // { key: 'energyStorageDepreciation', name: '储能折旧费(万元)' },
    // { key: 'electrolyzerDepreciation', name: '电解槽折旧费(万元)' },
    // { key: 'hydrogenStorageDepreciation', name: '储氢折旧费(万元)' },
    { key: 'maintenanceCost', name: '维修费(万元)' },
    // { key: 'powerMaintenanceCost', name: '发电维修费(万元)' },
    // { key: 'hydrogenMaintenanceCost', name: '制氢维修费(万元)' },
    // { key: 'windTurbineMaintenanceCost', name: '风机维修费(万元)' },
    // { key: 'photovoltaicMaintenanceCost', name: '光伏维修费(万元)' },
    // { key: 'energyStorageMaintenanceCost', name: '储能维修费(万元)' },
    // { key: 'electrolyzerMaintenanceCost', name: '电解槽维修费(万元)' },
    // { key: 'hydrogenStorageMaintenanceCost', name: '储氢维修费(万元)' },
    { key: 'insuranceCost', name: '保险费(万元)' },
    // { key: 'powerInsuranceCost', name: '发电保险费(万元)' },
    // { key: 'hydrogenInsuranceCost', name: '制氢保险费(万元)' },
    // { key: 'windTurbineInsuranceCost', name: '风机保险费(万元)' },
    // { key: 'photovoltaicInsuranceCost', name: '光伏保险费(万元)' },
    // { key: 'energyStorageInsuranceCost', name: '储能保险费(万元)' },
    // { key: 'electrolyzerInsuranceCost', name: '电解槽保险费(万元)' },
    // { key: 'hydrogenStorageInsuranceCost', name: '储氢保险费(万元)' },
    { key: 'waterCost', name: '水费(万元)' },
    // { key: 'hydrogenWaterCost', name: '制氢水费(万元)' },
    // { key: 'electrolyzerWaterCost', name: '电解槽水费(万元)' },
    { key: 'electricityCost', name: '电费(万元)' },
    // { key: 'hydrogenElectricityCost', name: '制氢电费(万元)' },
    // { key: 'electrolyzerElectricityCost', name: '电解槽电费(万元)' },
    { key: 'materialsCost', name: '材料费(万元)' },
    // { key: 'windTurbineMaterialsCost', name: '风机材料费(万元)' },
    // { key: 'photovoltaicMaterialsCost', name: '光伏材料费(万元)' },
    // { key: 'energyStorageMaterialsCost', name: '储能材料费(万元)' },
    // { key: 'electrolyzerMaterialsCost', name: '电解槽材料费(万元)' },
    // { key: 'hydrogenStorageMaterialsCost', name: '储氢材料费(万元)' },
    { key: 'transportationCost', name: '运输费(万元)' },
    // { key: 'hydrogenTransportationCost', name: '制氢运输费(万元)' },
    // { key: 'electrolyzerTransportationCost', name: '电解槽运输费(万元)' },
    { key: 'equipmentOverhaulReplacementCost', name: '设备大修更换费(万元)' },
    // { key: 'windTurbineOverhaulReplacementCost', name: '风机大修更换费(万元)' },
    // { key: 'photovoltaicOverhaulReplacementCost', name: '光伏大修更换费(万元)' },
    // { key: 'energyStorageOverhaulReplacementCost', name: '储能大修更换费(万元)' },
    // { key: 'electrolyzerOverhaulReplacementCost', name: '电解槽大修更换费(万元)' },
    // { key: 'hydrogenStorageOverhaulReplacementCost', name: '储氢大修更换费(万元)' },
    { key: 'landRentalFee', name: '土地租赁费(万元)' },
    { key: 'povertyAlleviationFee', name: '扶贫规费支出(万元)' },
    { key: 'landTaxFee', name: '土地税费(万元)' },
    // { key: 'windTurbineLandRentalFee', name: '风机土地租赁费(万元)' },
    // { key: 'photovoltaicLandRentalFee', name: '光伏土地租赁费(万元)' },
    // { key: 'windTurbineLandTaxFee', name: '风机土地税费(万元)' },
    // { key: 'photovoltaicLandTaxFee', name: '光伏土地税费(万元)' },
    { key: 'otherCost', name: '其他费用(万元)' },
    // { key: 'powerOtherCost', name: '发电其他费用(万元)' },
    // { key: 'hydrogenOtherCost', name: '制氢其他费用(万元)' },
    // { key: 'windTurbineOtherCost', name: '风机其他费用(万元)' },
    // { key: 'photovoltaicOtherCost', name: '光伏其他费用(万元)' },
    // { key: 'energyStorageOtherCost', name: '储能其他费用(万元)' },
    // { key: 'electrolyzerOtherCost', name: '电解槽其他费用(万元)' },
    // { key: 'hydrogenStorageOtherCost', name: '储氢其他费用(万元)' },
    { key: 'publicLoadPowerCost', name: '公辅装置用电成本(万元)' },
    { key: 'waterResourceTax', name: '水资源税(万元)' },
    { key: 'electrolyzerWaterResourceTax', name: '电解槽水资源税(万元)' },
    { key: 'laborCost', name: '工资福利及劳保统筹和住房基金(万元)' },
    { key: 'amortizationCost', name: '摊销费(万元)' },
    { key: 'maintenanceCostInputTax', name: '维修费进项税(万元)' },
    { key: 'materialsCostInputTax', name: '材料费进项税(万元)' },
    { key: 'transportationCostInputTax', name: '运输费进项税(万元)' },
    { key: 'waterCostInputTax', name: '水费进项税(万元)' },
    { key: 'electricityCostInputTax', name: '电费进项税(万元)' },
    { key: 'inputTax', name: '进项税(万元)' },
    { key: 'interestExpenseWithTax', name: '含税利息支出(万元)' },
    { key: 'fixedCost', name: '固定成本(万元)' },
    { key: 'variableCost', name: '可变成本(万元)' },
    { key: 'totalCostExpense', name: '总成本费用(万元)' }
  ],

  // 利润和利润分配表
  profitAndProfitDistribution: [
    { key: 'nonTaxOperatingIncome', name: '不含税营业收入(万元)' },
    { key: 'outputTax', name: '销项税(万元)' },
    { key: 'inputTaxEndingBalance', name: '进项税期末余额(万元)' },
    { key: 'inputTaxBeginningBalance', name: '进项税期初余额(万元)' },
    { key: 'currentPeriodInputTax', name: '本期进项税额(万元)' },
    { key: 'inputTaxDeduction', name: '进项税抵扣额(万元)' },
    { key: 'valueAddedTax', name: '应缴税金(万元)' },
    { key: 'inputTaxRemainingAmount', name: '进项税留底金额(万元)' },
    { key: 'cityConstructionTax', name: '城建税(万元)' },
    { key: 'totalEducationSurcharge', name: '总教育费附加(万元)' },
    { key: 'localEducationSurcharge', name: '地方教育费附加(万元)' },
    { key: 'salesTaxAndAdditions', name: '销售税金及附加(万元)' },
    { key: 'taxableSubsidy', name: '补贴收入应税(万元)' },
    { key: 'taxExemptSubsidy', name: '补贴收入免税(万元)' },
    { key: 'totalProfit', name: '利润总额(万元)' },
    { key: 'accumulatedTotalProfit', name: '累计利润总额(万元)' },
    { key: 'previousYearLossCompensation', name: '弥补以前年度亏损(万元)' },
    { key: 'taxableIncome', name: '应纳税所得额(万元)' },
    { key: 'incomeTax', name: '所得税(万元)' },
    { key: 'netProfit', name: '净利润(万元)' },
    { key: 'initialUndistributedProfit', name: '期初未分配的利润(万元)' },
    { key: 'statutoryReserveFund', name: '提取法定盈余公积金(万元)' },
    { key: 'profitAvailableForDistribution', name: '可供投资者分配的利润(万元)' },
    { key: 'payableProfit', name: '应付利润(万元)' },
    { key: 'undistributedProfit', name: '未分配利润(万元)' },
    { key: 'interestAndTaxProfit', name: '息税前利润(万元)' },
    { key: 'EBITDA', name: '息税折旧摊销前利润(万元)' }
  ],

  // 项目投资现金流量表
  projectInvestmentCashFlow: [
    { key: 'operatingIncomeWithTax', name: '含税营业收入(万元)' },
    { key: 'subsidyIncome', name: '补贴收入(万元)' },
    { key: 'fixedAssetsResidualValue', name: '固定资产残值(万元)' },
    { key: 'workingCapitalRecovery', name: '流动资金回收(万元)' },
    { key: 'projectInvestmentCashInflow', name: '项目投资现金流入(万元)' },
    { key: 'constructionInvestment', name: '建设投资(万元)' },
    { key: 'workingCapitalInvestment', name: '流动资金投资(万元)' },
    { key: 'operatingCost', name: '经营成本(万元)' },
    { key: 'valueAddedTaxInputPayment', name: '增值税进项税额(万元)' },
    { key: 'valueAddedTaxPayment', name: '增值税缴纳(万元)' },
    { key: 'projectInvestmentCashOutflow', name: '项目投资现金流出(万元)' },
    { key: 'netCashFlowBeforeIncomeTax', name: '所得税前净现金流量(万元)' },
    { key: 'accumulatedNetCashFlowBeforeIncomeTax', name: '累计所得税前净现金流量(万元)' },
    { key: 'adjustedIncomeTax', name: '调整所得税(万元)' },
    { key: 'netCashFlowAfterIncomeTax', name: '所得税后净现金流量(万元)' },
    { key: 'accumulatedNetCashFlowAfterIncomeTax', name: '累计所得税后净现金流量(万元)' },
    { key: 'dynamicPaybackPeriod_discountFactor', name: '动态回收期折现系数', isDecimal: true },
    { key: 'dynamicPaybackPeriod_presentValueBeforeTax', name: '动态回收期税前现值(万元)' },
    { key: 'dynamicPaybackPeriod_presentValueAfterTax', name: '动态回收期税后现值(万元)' },
    { key: 'dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax', name: '动态回收期累计税前净现金流量(万元)' },
    { key: 'dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax', name: '动态回收期累计税后净现金流量(万元)' },
    { key: 'accountsReceivable', name: '应收账款(万元)' },
    { key: 'interestCoverageRatio', name: '利息覆盖率(倍数)' },
    { key: 'debtCoverageRatio', name: '偿债覆盖率(倍数)' }
  ],

  // 资本金财务现金流量表
  equityCapitalCashFlow: [
    { key: 'operatingIncomeWithTax', name: '含税营业收入(万元)' },
    { key: 'subsidyIncome', name: '补贴收入(万元)' },
    { key: 'projectCapitalInvestment', name: '项目资本金投资(万元)' },
    { key: 'loanPrincipalRepayment', name: '偿还借款本金(万元)' },
    { key: 'annualInterest', name: '本年付息(万元)' },
    { key: 'shortTermLoanInterest', name: '短期借款利息(万元)' },
    { key: 'workingCapitalInterest', name: '流动资金利息(万元)' },
    { key: 'operatingCost', name: '经营成本(万元)' },
    { key: 'inputTaxPayment', name: '进项税支付(万元)' },
    { key: 'salesTaxAndSurcharge', name: '销售税金及附加(万元)' },
    { key: 'incomeTax', name: '所得税(万元)' },
    { key: 'vatPayable', name: '应缴增值税(万元)' },
    { key: 'fixedAssetsResidualValue', name: '固定资产残值(万元)' },
    { key: 'workingCapitalRecovery', name: '流动资金回收(万元)' },
    { key: 'payableProfit', name: '应付利润(万元)' },
    { key: 'loanInterestRepayment', name: '偿还借款利息(万元)' },
    { key: 'capitalCashInflow', name: '资本金现金流入(万元)' },
    { key: 'capitalCashOutflow', name: '资本金现金流出(万元)' },
    { key: 'capitalNetCashFlow', name: '资本金净现金流量(万元)' },
    { key: 'accumulatedNetCashFlow', name: '累计净现金流量(万元)' },
    { key: 'capitalPretaxNetCashFlow', name: '资本金税前净现金流量(万元)' },
    { key: 'accumulatedPretaxNetCashFlow', name: '累计税前净现金流量(万元)' },
    { key: 'discountFactor', name: '折现系数', isDecimal: true },
    { key: 'presentValue', name: '现值(万元)' },
    { key: 'accumulatedPresentValue', name: '累计现值(万元)' }
  ],

  // 财务计划现金流量表
  financialPlanCashFlow: [
    { key: 'operatingIncomeWithTax', name: '含税营业收入(万元)' },
    { key: 'salesTax', name: '销项税(万元)' },
    { key: 'taxableSubsidyIncome', name: '应税补贴收入(万元)' },
    { key: 'operatingActivityOtherInflow', name: '经营活动其他流入(万元)' },
    { key: 'operatingActivityCashInflow', name: '经营活动现金流入(万元)' },
    { key: 'operatingCost', name: '经营成本(万元)' },
    { key: 'inputTax', name: '进项税(万元)' },
    { key: 'salesTaxAndSurcharge', name: '销售税金及附加(万元)' },
    { key: 'vatPayable', name: '应缴增值税(万元)' },
    { key: 'incomeTax', name: '所得税(万元)' },
    { key: 'operatingActivityOtherOutflow', name: '经营活动其他流出(万元)' },
    { key: 'operatingActivityCashOutflow', name: '经营活动现金流出(万元)' },
    { key: 'operatingActivitiesNetCashFlow', name: '经营活动净现金流量(万元)' },
    { key: 'investmentActivityCashInflow', name: '投资活动现金流入(万元)' },
    { key: 'fixedAssetsResidualValue', name: '固定资产残值(万元)' },
    { key: 'workingCapitalRecovery', name: '流动资金回收(万元)' },
    { key: 'investmentActivityBuildingInvestment', name: '投资活动建设投资(万元)' },
    { key: 'investmentActivityOperationInvestment', name: '投资活动运营投资(万元)' },
    { key: 'investmentActivityWorkingCapital', name: '投资活动流动资金(万元)' },
    { key: 'investmentActivityOtherOutflow', name: '投资活动其他流出(万元)' },
    { key: 'investmentActivityCashOutflow', name: '投资活动现金流出(万元)' },
    { key: 'investmentActivitiesNetCashFlow', name: '投资活动净现金流量(万元)' },
    { key: 'financingActivityCashInflow', name: '筹资活动现金流入(万元)' },
    { key: 'equityCapitalInvestment', name: '资本金投资(万元)' },
    { key: 'buildingInvestmentLoan', name: '建设投资借款(万元)' },
    { key: 'workingCapitalLoan', name: '流动资金借款(万元)' },
    { key: 'bond', name: '债券(万元)' },
    { key: 'shortTermLoan', name: '短期借款(万元)' },
    { key: 'financingActivityOtherInflow', name: '筹资活动其他流入(万元)' },
    { key: 'interestExpense', name: '利息支出(万元)' },
    { key: 'debtPrincipalRepayment', name: '偿还债务本金(万元)' },
    { key: 'payableProfit', name: '应付利润(万元)' },
    { key: 'financingActivityOtherOutflow', name: '筹资活动其他流出(万元)' },
    { key: 'financingActivityCashOutflow', name: '筹资活动现金流出(万元)' },
    { key: 'financingActivitiesNetCashFlow', name: '筹资活动净现金流量(万元)' },
    { key: 'netCashFlow', name: '净现金流量(万元)' },
    { key: 'accumulatedSurplusFunds', name: '累计盈余资金(万元)' }
  ],

  // 资产负债表
  balanceSheet: [
    { key: 'totalCurrentAssets', name: '流动资产合计(万元)' },
    { key: 'accumulatedSurplusFunds', name: '累计盈余资金(万元)' },
    { key: 'currentAssets', name: '流动资产(万元)' },
    { key: 'constructionInProgress', name: '在建工程(万元)' },
    { key: 'netFixedAssets', name: '固定资产净值(万元)' },
    { key: 'netIntangibleAndOtherAssets', name: '无形资产及其他资产净值(万元)' },
    { key: 'vatDeductibleAssets', name: '增值税可抵扣资产(万元)' },
    { key: 'assets', name: '资产总计(万元)' },
    { key: 'totalCurrentLiabilities', name: '流动负债合计(万元)' },
    { key: 'currentYearShortTermLoan', name: '本年短期借款(万元)' },
    { key: 'otherLiabilities', name: '其他负债(万元)' },
    { key: 'constructionInvestmentLoan', name: '建设投资借款(万元)' },
    { key: 'workingCapitalLoan', name: '流动资金借款(万元)' },
    { key: 'totalLiabilities', name: '负债合计(万元)' },
    { key: 'ownersEquity', name: '所有者权益(万元)' },
    { key: 'capital', name: '实收资本(万元)' },
    { key: 'capitalReserve', name: '资本公积(万元)' },
    { key: 'accumulatedSurplusReserves', name: '累计盈余公积(万元)' },
    { key: 'accumulatedUndistributedProfits', name: '累计未分配利润(万元)' },
    { key: 'totalLiabilitiesAndEquity', name: '负债和所有者权益总计(万元)' },
    { key: 'assetLiabilityRatio', name: '资产负债率(%)', isPercentage: true }
  ]
}

// 年份横排表格数据的通用函数
export const generateYearlyTableData = (data, tableConfig, yearCount = 25) => {
  if (!data) return []
  
  return tableConfig.map((indicator, index) => {
    const row = {
      key: index + 1,
      indicator: indicator.name
    }
    
    for (let i = 1; i <= yearCount; i++) {
      const yearIndex = i - 1
      let value = '--'
      
      if (indicator.isFixed) {
        // 固定值，所有年份相同
        value = data[indicator.key] || '--'
      } else if (data[indicator.key] && data[indicator.key][yearIndex] !== undefined) {
        const rawValue = data[indicator.key][yearIndex]
        if (indicator.isPercentage) {
          value = formatPercentage(rawValue) + '%'
        } else if (indicator.isDecimal) {
          value = formatDecimal(rawValue)
        } else {
          value = formatNumber(rawValue)
        }
      }
      
      row[`year${i}`] = value
    }
    
    return row
  })
}

// 生成基础表格数据的通用函数
export const generateBasicTableData = (data, tableConfig, dataSource) => {
  if (!data) return []
  
  return tableConfig.map(config => {
    const value = data[config.nameKey]
    const formattedValue = config.formatter ? config.formatter(value) : formatNumber(value)
    
    return {
      key: config.key,
      name: config.name,
      item: config.name,
      amount: formattedValue,
      value: formattedValue,
      description: config.description
    }
  })
}

// 获取数据的通用函数，处理重复字段优先级
export const getDataValue = (resultData, field, tablePriority = 'financialIndicatorsSummary') => {
  // 检查 resultData 是否为空，如果是 Vue ref 对象则获取其 value
  const data = resultData?.value || resultData
  if (!data) {
    return null
  }
  
  // 优先从财务指标汇总表获取数据
  if (data[tablePriority] && data[tablePriority][field] !== undefined) {
    return data[tablePriority][field]
  }
  
  // 从其他表格查找
  const tables = [
    'fixedAssetsInvestmentEstimation',
    'projectOverallBudget', 
    'financingPlan',
    'investmentPlanAndFundRaising'
  ]
  
  for (const table of tables) {
    if (data[table] && data[table][field] !== undefined) {
      return data[table][field]
    }
  }
  
  return null
}

// 关键指标配置
export const keyIndicatorItems = [
  {
    dataKey: 'projectTotalInvestment',
    label: '总投资',
    unit: '万元',
    formatter: formatNumber
  },
  {
    dataKey: 'totalPowerGenerationProfit',
    label: '利润总额',
    unit: '万元',
    formatter: formatNumber
  },
  {
    dataKey: 'projectInvestmentFIRR_afterTax',
    label: '项目投资财务内部收益率(税后)',
    unit: '%',
    formatter: formatPercentage
  },
  {
    dataKey: 'lcoh_ori',
    label: 'LCOH',
    unit: '元/Nm³',
    formatter: formatDecimal
  }
]

// 汇总卡片配置
export const summaryCardsConfig = [
  {
    title: '投资',
    items: [
      { label: '项目总投资', dataKey: 'projectTotalInvestment', unit: '万元', formatter: formatNumber },
      { label: '固定资产静态投资', dataKey: 'fixedAssetsStaticInvestment', unit: '万元', formatter: formatNumber },
      { label: '建设期投资', dataKey: 'constructionPeriodInterest', unit: '万元', formatter: formatNumber },
      { label: '流动资金', dataKey: 'workingCapital', unit: '万元', formatter: formatNumber },
      { label: '资本金', dataKey: 'equityCapitalAmount', unit: '万元', formatter: formatNumber },
      { label: '银行贷款', dataKey: 'totalBankLoanAmount', unit: '万元', formatter: formatNumber },
      { label: '单瓦静态总投资', dataKey: 'staticInvestmentPerWatt', unit: '元/W', formatter: formatDecimal },
      { label: '单瓦动态总投资', dataKey: 'dynamicInvestmentPerWatt', unit: '元/W', formatter: formatDecimal },
    ]
  },
  {
    title: '成本与收入',
    items: [
      { label: '总营业收入', dataKey: 'totalSalesRevenue', unit: '万元', formatter: formatNumber },
      { label: '总成本费用', dataKey: 'totalCostExpense', unit: '万元', formatter: formatNumber },
      { label: '年平均运营成本', dataKey: 'averageAnnualOperatingCost', unit: '万元', formatter: formatNumber },
      // { label: '单位发电总成本', dataKey: 'totalGenerationCostPerUnit', unit: '元/kWh', formatter: formatDecimal },
      { label: '发电利润总额', dataKey: 'totalPowerGenerationProfit', unit: '万元', formatter: formatNumber },
      { label: '资本金净利润率ROE', dataKey: 'returnOnEquity', unit: '%', formatter: formatPercentage },
      { label: '总投资收益率ROI', dataKey: 'returnOnInvestment', unit: '%', formatter: formatPercentage },
      { label: '资产负债率', dataKey: 'assetLiabilityRatio', unit: '%', formatter: formatPercentage }
    ]
  },
  {
    title: '收益率与回收期',
    items: [
      // { label: '总投资收益率ROI', dataKey: 'returnOnInvestment', unit: '%', formatter: formatPercentage },
      { label: '项目投资财务内部收益率(税前)', dataKey: 'projectInvestmentFIRR_beforeTax', unit: '%', formatter: formatPercentage },
      { label: '项目投资财务内部收益率(税后)', dataKey: 'projectInvestmentFIRR_afterTax', unit: '%', formatter: formatPercentage },
      { label: '资本金财务内部收益率(税前)', dataKey: 'equityCapitalFIRR_beforeTax', unit: '%', formatter: formatPercentage },
      { label: '资本金财务内部收益率(税后)', dataKey: 'equityCapitalFIRR_afterTax', unit: '%', formatter: formatPercentage },
      { label: '项目投资财务净现值(税前)', dataKey: 'projectInvestmentNPV_beforeTax', unit: '万元', formatter: formatNumber },
      { label: '资本金财务净现值(税后)', dataKey: 'equityCapitalNPV_afterTax', unit: '万元', formatter: formatNumber },
      { label: '静态投资回收期', dataKey: 'paybackPeriodAfterTax_static', unit: '年', formatter: formatDecimal },
      { label: '动态投资回收期', dataKey: 'paybackPeriodAfterTax_dynamic', unit: '年', formatter: formatDecimal },
    ]
  },
  {
    title: '发电与制氢指标',
    items: [
      { label: '装机规模', dataKey: 'installedCapacity', unit: 'MW', formatter: formatDecimal },
      { label: '年均发电量', dataKey: 'averageAnnualGeneration', unit: '万kW·h', formatter: formatDecimal },
      { label: '上网总结算电量', dataKey: 'totalGridElectricity', unit: '万kW·h', formatter: formatDecimal },
      { label: '年均上网结算电量', dataKey: 'averageAnnualGridElectricity', unit: '万kW·h', formatter: formatDecimal },
      // { label: '单位发电总成本', dataKey: 'totalGenerationCostPerUnit', unit: '元/kWh', formatter: formatDecimal },
      // { label: '度电运营成本', dataKey: 'operatingCostPerKWh', unit: '元/kWh', formatter: formatDecimal },
      { label: '平准化度电成本LCOE', dataKey: 'lcoe_ori', unit: '元/kWh', formatter: formatDecimal },
      { label: '平准化制氢成本LCOH', dataKey: 'lcoh_ori', unit: '元/Nm³', formatter: formatDecimal }
    ]
  }
]
