<template>
  <div class="test-page">
    <h1>展开/收起功能测试</h1>
    
    <div class="part_wrap">
      <div class="params_section_header">
        <div class="p_title">绿电绿氢系统参数</div>
        <div class="expand_toggle_btn" @click="toggleParamsExpanded">
          <span class="toggle_text">{{ paramsExpanded ? '收起' : '展开' }}</span>
          <component
            :is="paramsExpanded ? UpOutlined : DownOutlined"
            class="toggle_icon"
            :class="{ 'expanded': paramsExpanded }"
          />
        </div>
      </div>
      <div class="params_content" :class="{ 'expanded': paramsExpanded }">
        <div class="params_card_list">
          <div class="params_card_item">
            <div class="params_card_content">
              <div class="params_card_title">光伏</div>
              <div class="params_list">
                <div class="params_item">
                  <div class="params_key">EPC投资(元/W):</div>
                  <div class="params_value">2.8</div>
                </div>
                <div class="params_item">
                  <div class="params_key">运维成本(元/W/年):</div>
                  <div class="params_value">0.05</div>
                </div>
                <div class="params_item">
                  <div class="params_key">输电损耗(%):</div>
                  <div class="params_value">0.00</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="params_card_item">
            <div class="params_card_content">
              <div class="params_card_title">风机</div>
              <div class="params_list">
                <div class="params_item">
                  <div class="params_key">EPC投资(元/W):</div>
                  <div class="params_value">3.2</div>
                </div>
                <div class="params_item">
                  <div class="params_key">运维成本(元/W/年):</div>
                  <div class="params_value">0.06</div>
                </div>
                <div class="params_item">
                  <div class="params_key">输电损耗(%):</div>
                  <div class="params_value">—</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="params_card_item">
            <div class="params_card_content">
              <div class="params_card_title">电网</div>
              <div class="params_list">
                <div class="params_item">
                  <div class="params_key">年最大下网比例(%):</div>
                  <div class="params_value">10.00</div>
                </div>
                <div class="params_item">
                  <div class="params_key">年最大上网比例(%):</div>
                  <div class="params_value">20.00</div>
                </div>
                <div class="params_item">
                  <div class="params_key">绿电上网价格(元/kwh):</div>
                  <div class="params_value">0.332</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="params_card_item">
            <div class="params_card_content">
              <div class="params_card_title">储能</div>
              <div class="params_list">
                <div class="params_item">
                  <div class="params_key">容量配比(%):</div>
                  <div class="params_value">10.00</div>
                </div>
                <div class="params_item">
                  <div class="params_key">EPC投资(元/Wh):</div>
                  <div class="params_value">0.7</div>
                </div>
                <div class="params_item">
                  <div class="params_key">运维成本(元/Wh/年):</div>
                  <div class="params_value">0.04</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue'

// 绿电绿氢系统参数展开/收起状态
const paramsExpanded = ref(false)

// 切换参数展开/收起状态
const toggleParamsExpanded = () => {
  paramsExpanded.value = !paramsExpanded.value
}
</script>

<style scoped lang="less">
@baseColor: #0360B0;

.test-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.part_wrap {
  margin-bottom: 20px;
}

.p_title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

// 绿电绿氢系统参数样式
.params_section_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  
  .p_title {
    margin-bottom: 0;
  }
  
  .expand_toggle_btn {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    user-select: none;
    
    &:hover {
      background-color: #e9ecef;
      border-color: @baseColor;
      color: @baseColor;
    }
    
    .toggle_text {
      font-size: 14px;
      margin-right: 6px;
      font-weight: 500;
    }
    
    .toggle_icon {
      font-size: 12px;
      transition: transform 0.3s ease;
      
      &.expanded {
        transform: rotate(180deg);
      }
    }
  }
}

.params_content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s ease-in-out;
  
  &.expanded {
    max-height: 2000px; // 足够大的值来容纳所有内容
  }
}

.params_card_list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px; // 负边距抵消卡片的margin
  padding-top: 15px;
}

.params_card_item {
  width: 25%; // 固定每行4个
  padding: 0 8px; // 左右内边距
  margin-bottom: 16px;
  box-sizing: border-box;

  .params_card_content {
    background-color: #fff;
    padding: 16px;
    border: 1px solid #f0f0f0;
    height: 100%; // 确保同行卡片高度一致

    .params_card_title {
      font-size: 14px;
      font-weight: 600;
      color: @baseColor;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .params_list {
      .params_item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .params_key {
          color: #666;
          flex: 1;
          margin-right: 8px;
          word-break: break-word;
        }

        .params_value {
          color: #333;
          font-weight: 500;
          text-align: right;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
