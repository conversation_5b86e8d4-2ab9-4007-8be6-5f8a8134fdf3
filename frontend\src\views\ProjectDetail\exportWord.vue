<template>
  <div>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { Document, Packer, Paragraph, TextRun, HeadingLevel, ImageRun, AlignmentType } from 'docx'
import { saveAs } from 'file-saver'
import dayjs from 'dayjs'
import { paramsList, outputInfo } from './util'
// 导入图片
// import doc2Img from '/doc2.png'


// 接收父组件传递的数据
const props = defineProps({
  detailData: {
    type: Object,
    default: () => ({})
  },
  solutionParams: {
    type: Object,
    default: () => ({})
  }
})

// 导出Word文档
const exportWordDoc = async () => {
  try {
    // 先生成所有内容，避免在Document构造函数中使用await
    const projectInfo = generateProjectInfo()
    const projectConfig = generateProjectConfig()
    const runResults = generateRunResults()
    const calculationLogic = await generateCalculationLogic()

    // 创建文档
    const doc = new Document({
      styles: {
        default: {
          heading1: {
            run: {
              color: "000000",
              bold: true,
              size: 24
            }
          },
          heading2: {
            run: {
              color: "000000",
              bold: true,
              size: 20
            }
          }
        }
      },
      sections: [
        {
          properties: {},
          children: [
            // 首页标题 - 第一行
            new Paragraph({
              children: [
                new TextRun({
                  text: `${props.solutionParams.projectName || 'XX'} 项目容量测算报告`,
                  size: 40,
                  bold: true,
                  color: "000000"
                })
              ],
              alignment: AlignmentType.CENTER,
              spacing: { before: 2000, after: 400 }
            }),
            // 首页标题 - 第二行
            new Paragraph({
              children: [
                new TextRun({
                  text: "河南中核五院研究设计有限公司",
                  size: 28,
                  bold: true,
                  color: "000000"
                })
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 2000 }
            }),
            // 分页符
            new Paragraph({
              children: [new TextRun({ text: "" })],
              pageBreakBefore: true
            }),

            // 一、项目信息
            new Paragraph({
              children: [
                new TextRun({
                  text: "一、项目信息",
                  bold: true,
                  size: 24,
                  color: "000000"
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...projectInfo,

            // 二、项目配置
            new Paragraph({
              children: [
                new TextRun({
                  text: "二、项目配置",
                  bold: true,
                  size: 24,
                  color: "000000"
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...projectConfig,

            // 三、运行结果
            new Paragraph({
              children: [
                new TextRun({
                  text: "三、运行结果",
                  bold: true,
                  size: 24,
                  color: "000000"
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...runResults,

            // 四、计算逻辑
            new Paragraph({
              children: [
                new TextRun({
                  text: "四、计算逻辑",
                  bold: true,
                  size: 24,
                  color: "000000"
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),
            ...calculationLogic
          ]
        }
      ]
    })

    // 生成并下载文档
    const blob = await Packer.toBlob(doc)
    const fileName = `${props.detailData.projectName || '项目'}_容量测算报告_${dayjs().format('YYYY-MM-DD')}.docx`
    saveAs(blob, fileName)

    message.success('Word文档导出成功')
  } catch (error) {
    console.error('导出Word文档失败:', error)
    message.error('导出Word文档失败')
  }
}

// 生成项目信息内容
const generateProjectInfo = () => {
  const content = []
  const projectInfoParams = [
    { label: '项目名称', name: 'projectName', unit: '' },
    { label: '客户名称', name: 'customer', unit: '' },
    { label: '项目周期', name: 'cycle', unit: '年' },
    { label: '年产氢量', name: 'h2Product', unit: '吨' },
    { label: '项目描述', name: 'projectDesc', unit: '' },
  ]

  projectInfoParams.forEach(param => {
    const value = props.solutionParams[param.name]
    const displayValue = value !== undefined && value !== null ? value.toString() : '—'

    content.push(new Paragraph({
      children: [
        new TextRun({
          text: `${param.label}${param.unit ? `(${param.unit})` : ''}：`,
          bold: true
        }),
        new TextRun({
          text: displayValue
        })
      ],
      spacing: { after: 100 }
    }))
  })

  return content
}

// 生成项目配置内容
const generateProjectConfig = () => {
  const content = []
  // 过滤掉项目信息，只保留当前拓扑存在的配置项
  const visibleParams = paramsList(props.solutionParams.topology).filter(item => item.visible && item.title !== '项目信息')

  let sectionIndex = 1
  visibleParams.forEach(item => {
    // 添加二级标题，序号递增（如2.1、2.2等）
    content.push(new Paragraph({
      children: [
        new TextRun({
          text: `2.${sectionIndex} ${item.title}配置`,
          bold: true,
          size: 20,
          color: "000000"
        })
      ],
      heading: HeadingLevel.HEADING_2,
      spacing: { before: 300, after: 150 }
    }))

    // 添加参数列表
    item.params.forEach(param => {
      const value = props.solutionParams[param.name]
      const displayValue = value !== undefined && value !== null ?
        (param.unit === '%' ? (value * 100).toFixed(2) : value) : '—'

      content.push(new Paragraph({
        children: [
          new TextRun({
            text: `${param.label}${param.unit ? `(${param.unit})` : ''}：`,
            bold: true
          }),
          new TextRun({
            text: displayValue.toString()
          })
        ],
        spacing: { after: 100 }
      }))
    })

    sectionIndex++
  })

  return content
}

// 生成运行结果内容
const generateRunResults = () => {
  const content = []
  const resultSections = outputInfo()

  let sectionIndex = 1
  resultSections.forEach(section => {
    // 跳过原来的储氢统计section，用储罐统计代替
    if (section.key === 'alkStorage') {
      return
    }

    // 添加二级标题，序号递增（如3.1、3.2等）
    content.push(new Paragraph({
      children: [
        new TextRun({
          text: `3.${sectionIndex} ${section.mainTitle}`,
          bold: true,
          size: 20,
          color: "000000"
        })
      ],
      heading: HeadingLevel.HEADING_2,
      spacing: { before: 300, after: 150 }
    }))

    // 如果有list，添加参数列表
    if (section.list) {
      section.list.forEach(item => {
        const value = props.detailData[item.name]
        let displayValue = '—'

        if (value !== undefined && value !== null) {
          if (item.numberType === 'ratio') {
            displayValue = (value * 100).toFixed(2)
          } else {
            displayValue = value.toString()
          }
        }

        content.push(new Paragraph({
          children: [
            new TextRun({
              text: `${item.label}${item.unit ? `(${item.unit})` : ''}：`,
              bold: true
            }),
            new TextRun({
              text: displayValue
            })
          ],
          spacing: { after: 100 }
        }))
      })
    }

    // 如果是电解槽产品，使用key-value形式显示
    if (section.key === 'alk' && props.detailData.alkList) {
      const alkData = props.detailData.alkList
      if (Array.isArray(alkData) && alkData.length > 0) {
        // 按型号分组统计数量
        const alkSummary = {}
        alkData.forEach(device => {
          const model = device.name || '未知型号'
          if (alkSummary[model]) {
            alkSummary[model] += (device.number || 1)
          } else {
            alkSummary[model] = (device.number || 1)
          }
        })

        // 显示电解槽型号和数量
        Object.entries(alkSummary).forEach(([model, count]) => {
          content.push(new Paragraph({
            children: [
              new TextRun({
                text: `${model}：`,
                bold: true
              }),
              new TextRun({
                text: `${count}台`
              })
            ],
            spacing: { after: 100 }
          }))
        })
      }
    }

    sectionIndex++
  })

  // 特殊处理：储罐统计（代替原来的3.6位置）
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: `3.${sectionIndex} 储罐统计`,
        bold: true,
        size: 20,
        color: "000000"
      })
    ],
    heading: HeadingLevel.HEADING_2,
    spacing: { before: 300, after: 150 }
  }))

  // 储罐厂商
  if (props.detailData.alkStoreCompany) {
    content.push(new Paragraph({
      children: [
        new TextRun({
          text: "储罐厂商：",
          bold: true
        }),
        new TextRun({
          text: props.detailData.alkStoreCompany
        })
      ],
      spacing: { after: 100 }
    }))
  }

  // 储罐容量
  if (props.detailData.volume) {
    content.push(new Paragraph({
      children: [
        new TextRun({
          text: "储罐容量(m³)：",
          bold: true
        }),
        new TextRun({
          text: props.detailData.volume.toString()
        })
      ],
      spacing: { after: 100 }
    }))
  }

  // 储罐个数
  if (props.detailData.alkStoreNum) {
    content.push(new Paragraph({
      children: [
        new TextRun({
          text: "储罐个数：",
          bold: true
        }),
        new TextRun({
          text: props.detailData.alkStoreNum.toString()
        })
      ],
      spacing: { after: 100 }
    }))
  }

  return content
}

// 生成计算逻辑内容
const generateCalculationLogic = async () => {
  const content = []

  // 动态处理计算逻辑数据
  // 添加标题
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '4.1 采用寻优算法快速进行迭代计算',
        bold: true,
        size: 20,
        color: "000000"
      })
    ],
    heading: HeadingLevel.HEADING_2,
    spacing: { before: 300, after: 150 }
  }))

  // 添加内容段落
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '    采用粒子群算法进行多目标、多边界约束条件的寻优。其中粒子群算法是一种智能优化算法，本质上是在满足特定技术约束和经济目标的前提下，为一套包含风电、光伏、储能电池和电解制氢系统的混合能源系统，选择各组件的最优额定功率和容量。'
      })
    ],
    spacing: { after: 200 }
  }))

  // 添加图片
  // 确保图片数据有效
  // try {
  //   const imageBuffer = await loadImageFromImport(doc2Img)

  //   // 确保图片数据有效
  //   if (imageBuffer && imageBuffer.byteLength > 0) {
  //     content.push(new Paragraph({
  //       children: [
  //         new ImageRun({
  //           type: 'png',
  //           data: imageBuffer,
  //           transformation: {
  //             width: 400,
  //             height: 300,
  //           }
  //         })
  //       ],
  //       alignment: AlignmentType.CENTER,
  //       spacing: { after: 300 }
  //     }))
  //   } else {
  //     throw new Error('图片数据为空')
  //   }
  // } catch (error) {
  //   console.warn(`无法加载图片:`, error)
  //   // 如果图片加载失败，添加一个占位文本
  //   content.push(new Paragraph({
  //     children: [
  //       new TextRun({
  //         text: `[图片加载失败:`,
  //         italics: true,
  //         color: "666666"
  //       })
  //     ],
  //     alignment: AlignmentType.CENTER,
  //     spacing: { after: 300 }
  //   }))
  // }

  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '4.2 风光储氢容量计算逻辑',
        bold: true,
        size: 20,
        color: "000000"
      })
    ],
    heading: HeadingLevel.HEADING_2,
    spacing: { before: 300, after: 150 }
  }))
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '4.2.1 项目类型明确',
        bold: true,
        size: 20,
        color: "000000"
      })
    ],
    heading: HeadingLevel.HEADING_3,
    spacing: { before: 300, after: 150 }
  }))

  // 添加内容段落
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '    并网型：目标是减少对电网的依赖（自发自用）、赚取收益（卖电/卖氢）、实现碳中和。优化时需考虑电网电价（峰谷差价）、交互功率限制。离网型：目标是完全独立满足特定区域的电、氢负荷，可靠性是首要目标。优化核心是保证供电/氢的连续性。'
      })
    ],
    spacing: { after: 200 }
  }))
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '    离网型：目标是完全独立满足特定区域的电、氢负荷，可靠性是首要目标。优化核心是保证供电/氢的连续性。'
      })
    ],
    spacing: { after: 200 }
  }))

  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '4.2.2 优化目标',
        bold: true,
        size: 20,
        color: "000000"
      })
    ],
    heading: HeadingLevel.HEADING_3,
    spacing: { before: 300, after: 150 }
  }))
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '   经济性优先：最小化全生命周期总成本。'
      })
    ],
    spacing: { after: 200 }
  }))
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '    可靠性优先：在满足特定可靠性指标下（如缺电率LPSP<2%），使成本最低。'
      })
    ],
    spacing: { after: 200 }
  }))
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '    多目标优化：同时考虑经济性、可靠性、环保性，寻求帕累托最优解集。'
      })
    ],
    spacing: { after: 200 }
  }))

  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '4.2.3 系统结构',
        bold: true,
        size: 20,
        color: "000000"
      })
    ],
    heading: HeadingLevel.HEADING_3,
    spacing: { before: 300, after: 150 }
  }))
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '   明确系统包含哪些组件：风机、光伏、电池储能、电解槽、储氢罐等。'
      })
    ],
    spacing: { after: 200 }
  }))

  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '4.2.4 输入数据准备',
        bold: true,
        size: 20,
        color: "000000"
      })
    ],
    heading: HeadingLevel.HEADING_3,
    spacing: { before: 300, after: 150 }
  }))
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '   资源数据。整年时序数据，时间分辨率至少为1小时。风电、光伏8760小时出力曲线'
      })
    ],
    spacing: { after: 200 }
  }))
   content.push(new Paragraph({
    children: [
      new TextRun({
        text: '   负荷数据。电负荷：用户或电网所需的每小时用电功率；氢负荷：用户所需的每小时用氢质量或体积（若为纯制氢站，则此为核心需求）。'
      })
    ],
    spacing: { after: 200 }
  }))
   content.push(new Paragraph({
    children: [
      new TextRun({
        text: '   技术经济参数。成本数据：各组件的单位功率投资成本、单位容量投资成本、O&M成本、更换成本、残值；性能数据：性能数据：风机功率曲线、光伏功率曲线、电解槽效率（及部分负载特性）、电池充放电效率、自放电率、循环寿命、储氢系统效率;运行约束：电解槽最小启动功率、爬坡速率；电池荷电状态上下限、最大充放电功率.'
      })
    ],
    spacing: { after: 200 }
  }))
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '4.2.5 迭代计算',
        bold: true,
        size: 20,
        color: "000000"
      })
    ],
    heading: HeadingLevel.HEADING_3,
    spacing: { before: 300, after: 150 }
  }))
  content.push(new Paragraph({
    children: [
      new TextRun({
        text: '   采用智能优化算法：粒子群算法、遗传算法'
      })
    ],
    spacing: { after: 200 }
  }))
   content.push(new Paragraph({
    children: [
      new TextRun({
        text: '4.2.6 结果输出及数据分析',
        bold: true,
        size: 20,
        color: "000000"
      })
    ],
    heading: HeadingLevel.HEADING_3,
    spacing: { before: 300, after: 150 }
  }))

  return content
}

// 使用Canvas处理图片，确保格式正确
const loadImageFromImport = async (imageUrl) => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = () => {
      try {
        // 创建canvas
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        canvas.width = img.width
        canvas.height = img.height

        // 绘制图片到canvas
        ctx.drawImage(img, 0, 0)

        // 转换为blob
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error('无法转换图片为blob'))
            return
          }

          // 转换为ArrayBuffer
          const reader = new FileReader()
          reader.onload = () => {
            const arrayBuffer = reader.result
            console.log('图片处理成功，大小:', arrayBuffer.byteLength, 'bytes')
            resolve(new Uint8Array(arrayBuffer))
          }
          reader.onerror = () => reject(new Error('读取图片数据失败'))
          reader.readAsArrayBuffer(blob)
        }, 'image/png')

      } catch (error) {
        reject(error)
      }
    }

    img.onerror = () => reject(new Error('图片加载失败'))
    img.src = imageUrl

    console.log('开始加载图片:', imageUrl)
  })
}

// 暴露导出方法给父组件
defineExpose({
  exportWordDoc
})
</script>

<style scoped>
/* 这个组件不需要样式 */
</style>