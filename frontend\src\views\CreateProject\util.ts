export const formBasicInfo = ($t: (p: string) => void) => {
  return [
    { label: '项目名称', name: 'projectName', unit: '', default: undefined,  rules: [{ required: true, message:  '请输入' }], type: 'string' },
    { label: '客户名称', name: 'customer', unit: '', default: undefined, rules: [{ required: true, message:  '请输入' }], type: 'string' },
    { label: '项目周期', name: 'cycle', unit: '年', default: undefined,  rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { label: '年产氢量', name: 'h2Product', unit: '吨', default: undefined,  rules: [{ required: false, message:  '请输入' }], type: 'number' },
    // { label: '贷款利率', name: 'loanRate', unit: '', default: undefined,  rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio' },
    // { label: '贷款周期', name: 'loanCycle', unit: '年', default: undefined,  rules: [{ required: false, message:  '请输入' }], type: 'number' },
    // { label: '贷款比例', name: 'loanRadio', unit: '', default: undefined,  rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio' },
    { label: '项目描述', name: 'desc', unit: '', default: undefined,  rules: [{ required: false, message:  '请输入' }], type: 'textarea' },
  ]
}

export const formGoalList = () => {
  return [
    { label: 'LCOH最低', name: 'target0', unit: '', default: 1,  rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { label: '投资成本最低', name: 'target1', unit: '', default: 0,  rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { label: '弃电率最低', name: 'target2', unit: '', default: 0,  rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { label: '产氢量最大', name: 'target3', unit: '', default: 0,  rules: [{ required: false, message:  '请输入' }], type: 'number' },
  ]
}

export const formAlgList = () => {
  return [
    { label: '快速测算', name: '', default: 1 }
  ]
}
// pv
export const formPV = () => {
  return [
    { label: 'EPC投资', name: 'pv_epc', unit: '元/W', default: 2.9, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 0 },
    // { label: '运维比例', name: 'pv_om_radio', unit: '%', default: 0.25, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: 0 },
    { label: '运维成本', name: 'pv_om_cost', unit: '元/W/年', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 0  },
    { label: '输电损耗', name: 'pv_line_loss_rate', unit: '%', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number',numberType: 'ratio', visible: true, tag: 0  },
  ]
}
// wind
export const formWind = () => {
  return [
    { label: 'EPC投资', name: 'wind_epc', unit: '元/W', default: 4.2, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 1 },
    // { label: '运维比例', name: 'wind_om_radio', unit: '%', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: 1 },
    { label: '运维成本', name: 'wind_om_cost', unit: '元/W/年', default: 0.3, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 1 },
    { label: '输电损耗', name: 'wind_line_loss_rate', unit: '%', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number',numberType: 'ratio', visible: true, tag: 0  },

    
  ]
}

// 
// alk
export const formAlk = () => {
  return [
    { 
      label: '用水价格', name: 'water_price', unit: '元/吨', default: undefined, rules: [{ required: true, message:  '请输入' }],
      type: 'number',
      // options: [
      //   { label: '4.38元/吨', value: 4.38 },
      // ]
    },
    { label: '制氢耗水量', name: 'h2_water_consuming', unit: 'L/Nm³', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { 
      label: '制氢策略', name: 'ele_policy', unit: '', default: undefined,
      rules: [{ required: false, message:  '请输入' }],
      type: 'select',
      options: [
        { label: '满功率', value: 1 },
        { label: '均分',   value: 2 },
        { label: '轮值', value: 3 },
      ]
    },
    // { label: '制氢系统投资', name: 'h2_invest', unit: '元/W', default: 2, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 4  },
    // { label: '制氢厂房投资', name: 'plant_invest', unit: '元/W', default: 1.5, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 4 },
    { label: '运维比例', name: 'h2_om_radio', unit: '%', default: 0.02, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag:4  },
    // { label: '制氢运维成本', name: 'h2_om_cost', unit: '元/W/年', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 4 },

  ]
}

export const alkColumns = () => {
  return [
    {
      title: '厂商',
      dataIndex: 'manufacturer',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '产品型号',
      dataIndex: 'model',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '类型',
      dataIndex: 'ele_type',
      key: 'ele_type',
      s_type: 'select',
      rules: [{ required: false }],
      unit: '',
      options: [
        { label: 'ALK', value: 1, color: 'blue' },
        { label: 'PEM', value: 2, color: 'green'  },
      ]
    },
    {
      title: '容量',
      dataIndex: 'capacity',
    },
    {
      title: '系统价格(元/套)',
      dataIndex: 'price',
    },
    {
      title: '电耗(kWh/Nm³)',
      dataIndex: 'power_consumption',
    },
    {
      title: '额定功率(MW)',
      dataIndex: 'pe'
    },
    {
      title: '最低负载率',
      dataIndex: 'lower_load_rate', 
    },
    {
      title: '最高负载率',
      dataIndex: 'upper_load_rate',
    },
    // {
    //   title: '年衰减曲线',
    //   dataIndex: 'damp_curve',
    // },
    // {
    //   title: '制氢电源效率',
    //   dataIndex: 'power_supply_efficiency',
    // },
    {
      title: '辅助系统能耗(kWh/Nm³)',
      dataIndex: 'assist_consumption',
    },  
  ];
}

// grid
export const formGrid = () => {
  return [
    { label: '年最大下网比例', name: 'grid_down_radio', unit: '%', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', max: 1, numberType: 'ratio' },
    { label: '年最大上网比例', name: 'grid_up_radio', unit: '%', default: 0.2, rules: [{ required: false, message:  '请输入' }], type: 'number', max: 1, numberType: 'ratio' },
    {
      label: '绿电上网价格(元/kwh)', name: 'grid_sale_price',
      unit: '', default: undefined, rules: [{ required: false, message:  '请输入' }],
      type: 'number',
      // options: [
      //   { label: '0.332元/kwh', value: 0.332 },
      // ]
    },
  ]
}

// bat
export const formBat = () => {
  return [
    {
      label: '控制策略', name: 'es_policy',
      unit: '', default: undefined, rules: [{ required: false, message:  '请输入' }],
      type: 'select',
      options: [
        { label: '削峰填谷', value: 1 },
      ]
    },
    { label: '最低配置比例', name: 'es_min_radio', unit: '', default: 0.1, rules: [{ required: false, message:  '请输入' }], type: 'number', max: 1, numberType: 'ratio' },
    { label: 'EPC投资', name: 'es_epc', unit: '元/Wh', default: 1.3, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 3 },
    // { label: '储能运维比例', name: 'es_om_radio', unit: '%', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: 3 },
    { label: '运维成本', name: 'es_om_cost', unit: '元/Wh/年', default: 0.0675, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 3 },
  ]
}
export const batColumns = () => {
  return [
    {
      title: '厂商',
      dataIndex: 'manufacturer',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '产品型号',
      dataIndex: 'model',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '单机功率(MW)',
      dataIndex: 'single_power',
    },
    {
      title: '充电效率',
      dataIndex: 'charge_efficiency',
    },
    {
      title: '放电效率',
      dataIndex: 'discharge_efficiency',
    },
    {
      title: '充放电倍率',
      dataIndex: 'c_rate',
    },
    // {
    //   title: '储能占比',
    //   dataIndex: 'radio'
    // },
    {
      title: '初始SOC',
      dataIndex: 'init_soc', 
    },
    {
      title: 'SOC下限',
      dataIndex: 'min_soc',
    },
    {
      title: 'SOC上限',
      dataIndex: 'max_soc',
    },
    // {
    //   title: '置信度',
    //   dataIndex: 'confidence',
    // },
    {
      title: '寿命(年)',
      dataIndex: 'life_cycle',
    },  
  ];
}

// alk storage
export const formAlkStorage = () => {
  return [
    // { label: '储罐容量下限(kg)', name: 'hs_min_capacity', unit: '', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number' },
    // { label: '储罐容量上限(kg)', name: 'hs_max_capacity', unit: '', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { label: '最大升负荷速率', name: 'max_increase_load_rate', unit: '%', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number',numberType: 'ratio'  },
    { label: '最大降负荷速率', name: 'max_down_load_rate', unit: '%', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio'  },
    { label: '供氢上限', name: 'max_supply_h2', unit: 'Nm³/h', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number'  },
    { label: '供氢下限', name: 'min_supply_h2', unit: 'Nm³/h', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number'  },
    { label: '负荷调整时间', name: 'adjust_time', unit: 'min', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { label: '负荷调节间隔', name: 'adjust_interval', unit: 'min', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { label: '供氢量', name: 'supply_h2', unit: 'Nm³/h', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { label: 'EPC投资', name: 'hs_invest', unit: '元/m³', default: 2, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 5 },
    { label: '运维比例(%)', name: 'hs_om_radio', unit: '', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: 5 },
    // { label: '储氢运维成本', name: 'hs_om_cost', unit: '元/kg/年', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 5 },
  
  ]
}

export const alkStorageColumns = () => {
  return [
    {
      title: '厂商',
      dataIndex: 'manufacturer',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '产品型号',
      dataIndex: 'model',
      s_type: 'string',
      rules: [{ required: false }],
      unit: ''
    },
    {
      title: '体积(m³)',
      dataIndex: 'volume',
    },
    {
      title: '最小运行压力(Mpa)',
      dataIndex: 'min_pressure',
    },
    {
      title: '最大运行压力(Mpa)',
      dataIndex: 'max_pressure',
    },
    {
      title: '价格(元)',
      dataIndex: 'price',
    },
    {
      title: '占地面积(㎡)',
      dataIndex: 'area'
    }
  ];
}
// 成本
// pv: 0, wind: 1, grid: 2, bat: 3, zhiqing: 4, chuqing: 5
// old-投资成本
export const investCost = () => {
  return [
    { label: '光伏EPC投资', name: 'pv_epc', unit: '元/W', default: 2.9, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 0 },
    { label: '风电EPC投资', name: 'wind_epc', unit: '元/W', default: 4.2, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 1 },
    { label: '储能EPC投资', name: 'es_epc', unit: '元/Wh', default: 1.3, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 3 },
    { label: '制氢系统投资', name: 'h2_invest', unit: '元/W', default: 2, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 4  },
    { label: '制氢厂房投资', name: 'plant_invest', unit: '元/W', default: 1.5, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 4 },
    { label: '储氢系统投资', name: 'hs_invest', unit: '元/Nm³', default: 2, rules: [{ required: true, message:  '请输入' }], type: 'number', visible: true, tag: 5 },
  ]
}
// old-运维成本
export const operationCost = () => {
  return [
    { label: '光伏运维比例', name: 'pv_om_radio', unit: '', default: 0.25, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: 0 },
    { label: '光伏运维成本', name: 'pv_om_cost', unit: '元/W/年', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 0  },
    { label: '风电运维比例', name: 'wind_om_radio', unit: '', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: 1 },
    { label: '风电运维成本', name: 'wind_om_cost', unit: '元/W/年', default: 0.3, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 1 },
    { label: '储能运维比例', name: 'es_om_radio', unit: '', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: 3 },
    { label: '储能运维成本', name: 'es_om_cost', unit: '元/Wh/年', default: 0.0675, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 3 },
    { label: '制氢运维比例', name: 'h2_om_radio', unit: '', default: 0.02, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag:4  },
    { label: '制氢运维成本', name: 'h2_om_cost', unit: '元/W/年', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 4 },
    { label: '储氢运维比例', name: 'hs_om_radio', unit: '', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: 5 },
    { label: '储氢运维成本', name: 'hs_om_cost', unit: '元/kg/年', default: undefined, rules: [{ required: false, message:  '请输入' }], type: 'number', visible: true, tag: 5 },
    { label: '设备折现率', name: 'discount_rate', unit: '', default: undefined, rules: [{ required: true, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: -1 },
    { label: '负荷缺失率', name: 'load_miss', unit: '', default: 0, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: -1 },
  ]
}

// 其他配置
export const otherCost = () => {
  return [
    { label: '贷款周期', name: 'loanCycle', unit: '年', default: undefined,  rules: [{ required: false, message:  '请输入' }], type: 'number' },
    { label: '贷款比例', name: 'loanRadio', unit: '%', default: undefined,  rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio' },
    { label: '贷款利率', name: 'loanRate', unit: '%', default: undefined,  rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio' },
    { label: '设备折现率', name: 'discount_rate', unit: '%', default: undefined, rules: [{ required: true, message:  '请输入' }], type: 'number', numberType: 'ratio', tag: -1 },
    // { label: '负荷缺失率', name: 'load_miss', unit: '%', default: 0, rules: [{ required: false, message:  '请输入' }], type: 'number', numberType: 'ratio', visible: true, tag: -1 },
  ]
}

