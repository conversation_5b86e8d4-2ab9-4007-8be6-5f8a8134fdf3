<template>
  <div class="body_wrap">
    <div class="p_wrap">
      <div class="content_wrap">
        <div class="part_wrap">
          <div class="p_title">
            <div class="title-wrapper">
              <a-breadcrumb>
                <a-breadcrumb-item><a  @click="goBack"> < 返回</a></a-breadcrumb-item>
                <a-breadcrumb-item>方案对比</a-breadcrumb-item>
              </a-breadcrumb>
            </div>
            <div class="btn_wrap">
              <!-- 保留原有的其他按钮，如果有的话 -->
            </div>
          </div>
          
          <!-- 财务指标对比表 -->
           <div style="font-size: 16px;font-weight: 700;margin:  20px 0;">指标对比</div>
          <div class="table_wrap">
            <!-- 数据加载中的状态 -->
            <div v-if="loading || solutions.length === 0 || tableData.length === 0" class="loading-state">
              <a-spin size="large" />
              <div class="loading-text">正在加载方案对比数据...</div>
            </div>
            
            <!-- 只有在数据加载完成后才渲染表格 -->
            <a-table
              v-else
              size="small"
              bordered
              class="compare_table"
              :columns="tableColumns"
              :data-source="tableData"
              :pagination="false"
              :row-key="record => record.key"
              :defaultExpandAllRows="true"
              
            >
              <!-- 自定义表头 -->
              <template #headerCell="{ column }">
                <template v-if="column.customTitle">
                  <div class="solution-header">
                    <span>{{ column.title }}</span>
                    <LinkOutlined 
                      class="detail-link-icon" 
                      @click="goToDetail(column.solutionData)"
                      title="查看详情"
                    />
                  </div>
                </template>
                <template v-else>
                  {{ column.title }}
                </template>
              </template>
              
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'indicator'">
                  <span class="indicator-name" :class="{ 
                    'parent-indicator': record.isParent,
                    'child-indicator': record.isChild,
                    'single-indicator': record.isSingle
                  }">
                    {{ record.name }}
                    <span v-if="record.unit" class="unit-text">（{{ record.unit }}）</span>
                  </span>
                </template>
                <template v-else>
                  <div class="value-cell" v-if="record.isChild || record.isSingle || (record.isParent && record.unit)">
                    <span>{{ formatValue(record[column.key], record.unit) }}</span>
                    <StarFilled 
                      v-if="record.bestSolutions && record.bestSolutions.includes(column.key)" 
                      class="best-icon"
                    />
                  </div>
                  <div v-else class="parent-cell">
                    <!-- 没有单位的父级行数据列显示为空 -->
                  </div>
                </template>
              </template>
              
              <template #emptyText>
                <div class="empty-state">
                  <!-- <div class="empty-icon">📊</div> -->
                  <div class="empty-text">暂无方案对比数据</div>
                </div>
              </template>
            </a-table>
          </div>
          
          <!-- 方案分析 -->
          <div class="analysis_wrap" v-if="analysisResult.length > 0">
            <div class="analysis_title">
              <!-- <span class="title_icon">📊</span> -->
              方案分析
            </div>
            <div class="analysis_content">
              <div class="analysis_item" v-for="(item, index) in analysisResult" :key="index">
                <div class="analysis_label">{{ item.label }}：</div>
                <div class="analysis_value">{{ item.value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { StarOutlined, StarFilled, LinkOutlined } from '@ant-design/icons-vue'
import { getEconomicCompareResult } from '@/api/economic'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const solutions = ref([])
const analysisResult = ref([])

// 表格列配置
const tableColumns = computed(() => {
  const columns = [
    {
      title: '财务指标',
      key: 'indicator',
      dataIndex: 'name',
      width: 240,
      fixed: 'left'
    }
  ]
  
  // 根据方案数量动态计算列宽
  const solutionCount = solutions.value.length
  let columnWidth = 150 // 默认宽度
  
  if (solutionCount <= 2) {
    columnWidth = 200
  } else if (solutionCount === 3) {
    columnWidth = 180
  } else if (solutionCount === 4) {
    columnWidth = 160
  } else if (solutionCount === 5) {
    columnWidth = 140
  }
  
  // 为每个方案添加列
  solutions.value.forEach((solution, index) => {
    // 从接口数据中获取方案名称
    const solutionName = solution.solution?.name || `方案${index + 1}`
    columns.push({
      title: solutionName,
      key: `solution_${solution.solution?.id || index}`,
      width: columnWidth,
      align: 'center',
      // 添加自定义标题模板
      customTitle: true,
      solutionData: solution // 保存方案数据用于跳转
    })
  })
  
  return columns
})

// 表格数据
const tableData = computed(() => {
  if (solutions.value.length === 0) return []
  
  // 构建层级化的指标数据 - 按照table.md的实际结构
  const indicatorGroups = [
    // 只有一级的指标
    {
      key: 'averageAnnualGeneration',
      name: '年均发电量',
      unit: '万kW·h',
      isSingle: true
    },
    
    // 有子级的指标组
    {
      key: 'investment',
      name: '项目总投资',
      children: [
        { key: 'fixedAssetsStaticInvestment', name: '固定资产静态投资', unit: '万元' },
        // { key: 'constructionPeriodInterest', name: '建设期利息', unit: '万元' },
        // { key: 'workingCapital', name: '流动资金', unit: '万元' },
        { key: 'staticInvestmentPerWatt', name: '单瓦静态总投资', unit: '元/W' },
        { key: 'dynamicInvestmentPerWatt', name: '单瓦动态总投资', unit: '元/W' },
        // { key: 'investmentCostPerKWh', name: '度电投资成本', unit: '元/kWh' }
      ]
    },
    {
      key: 'cost',
      name: '总成本费用',
      children: [
        { key: 'averageAnnualOperatingCost', name: '年平均运营成本', unit: '万元' },
        { key: 'totalGenerationCostPerUnit', name: '单位发电总成本', unit: '元/kWh' },
        { key: 'operatingCostPerKWh', name: '度电运营成本', unit: '元/kWh' },
        { key: 'lcoh', name: '平准化制氢成本LCOH', unit: '元/Nm³', optimize: 'min' },
        { key: 'lcoe', name: '平准化度电成本LCOE', unit: '元/kWh' }
      ]
    },
    {
      key: 'profitability',
      name: '项目投资财务内部收益率_税后',
      children: [
        { key: 'projectInvestmentFIRR_afterTax', name: '项目投资财务内部收益率(税后)', unit: '%' },
        { key: 'equityCapitalFIRR_afterTax', name: '资本金财务内部收益率(税后)', unit: '%' },
        // { key: 'totalPowerGenerationProfit', name: '发电利润总额', unit: '万元' },
        { key: 'projectInvestmentNPV_beforeTax', name: '项目投资财务税前净现值', unit: '万元' },
        { key: 'equityCapitalNPV_afterTax', name: '资本金财务税后净现值', unit: '万元' },
        { key: 'returnOnInvestment', name: '总投资收益率ROI', unit: '%' },
        { key: 'returnOnEquity', name: '项目资本金净利润率ROE', unit: '%' },
      ]
    },
    
    // 只有一级的指标
    {
      key: 'paybackPeriodAfterTax_static',
      name: '税后投资回收期(静态)',
      unit: '年',
      isSingle: true,
      optimize: 'min'
    },
    {
      key: 'paybackPeriodAfterTax_dynamic',
      name: '税后投资回收期(动态)',
      unit: '年',
      isSingle: true
    }
  ]
  
  const tableRows = []
  
  indicatorGroups.forEach(group => {
    if (group.isSingle) {
      // 单级指标，直接作为一行，没有children属性
      const singleRow = {
        key: group.key,
        name: group.name,
        unit: group.unit,
        optimize: group.optimize,
        isSingle: true
        // 注意：单级指标不设置children属性，这样就不会显示展开按钮
      }
      
      // 获取各个方案的数据
      const values = []
      solutions.value.forEach((solution, index) => {
        const columnKey = `solution_${solution.solution?.id || index}`
        const financialData = solution.result?.resultTables?.financialIndicatorsSummary || {}
        let value = financialData[group.key] || 0
        
        // 百分比指标处理
        if (group.unit === '%') {
          if (value < 1 && value > 0) {
            value = (value * 100).toFixed(2)
          } else {
            value = value.toFixed(2)
          }
        } else if (typeof value === 'number') {
          value = value.toFixed(2)
        }
        
        singleRow[columnKey] = value
        values.push({ key: columnKey, value: parseFloat(value) || 0 })
      })
      
      // 找出最优值
      if (group.optimize) {
        const bestSolutions = findBestSolutions(values, group.optimize)
        singleRow.bestSolutions = bestSolutions
      }
      
      tableRows.push(singleRow)
    } else {
      // 多级指标，有父子结构
      const parentRow = {
        key: group.key,
        name: group.name,
        children: [],
        isParent: true
      }
      
      // 确定父级指标的主要字段和单位
      let parentMainKey = ''
      let parentUnit = ''
      
             // 根据父级分类确定主要指标
       if (group.key === 'investment') {
         parentMainKey = 'projectTotalInvestment'
         parentUnit = '万元'
       } else if (group.key === 'cost') {
         // 如果totalCostExpense为0，使用averageAnnualOperatingCost作为替代
         parentMainKey = 'averageAnnualOperatingCost'
         parentUnit = '万元'
       } else if (group.key === 'profitability') {
         parentMainKey = 'projectInvestmentFIRR_afterTax'
         parentUnit = '%'
       }
      
      // 为父级行添加主要指标的数据
      if (parentMainKey) {
        parentRow.unit = parentUnit
        const parentValues = []
        solutions.value.forEach((solution, index) => {
          const columnKey = `solution_${solution.solution?.id || index}`
          const financialData = solution.result?.resultTables?.financialIndicatorsSummary || {}
          let value = financialData[parentMainKey] || 0
          
          // 百分比指标处理
          if (parentUnit === '%') {
            if (value < 1 && value > 0) {
              value = (value * 100).toFixed(2)
            } else {
              value = value.toFixed(2)
            }
          } else if (typeof value === 'number') {
            value = value.toFixed(2)
          }
          
          parentRow[columnKey] = value
          parentValues.push({ key: columnKey, value: parseFloat(value) || 0 })
        })
        
        // 为父级指标找出最优值
        if (group.key === 'investment') {
          const bestSolutions = findBestSolutions(parentValues, 'min')
          parentRow.bestSolutions = bestSolutions
        } else if (group.key === 'profitability') {
          const bestSolutions = findBestSolutions(parentValues, 'max')
          parentRow.bestSolutions = bestSolutions
        }
      } else {
        // 没有主要指标的父级行，数据列显示为空
        solutions.value.forEach((solution, index) => {
          const columnKey = `solution_${solution.solution?.id || index}`
          parentRow[columnKey] = ''
        })
      }
      
      // 子级行
      group.children.forEach(indicator => {
        const childRow = {
          key: indicator.key,
          name: indicator.name,
          unit: indicator.unit,
          optimize: indicator.optimize,
          isChild: true
        }
        
        // 获取各个方案的数据
        const values = []
        solutions.value.forEach((solution, index) => {
          const columnKey = `solution_${solution.solution?.id || index}`
          const financialData = solution.result?.resultTables?.financialIndicatorsSummary || {}
          let value = financialData[indicator.key] || 0
          
          // 百分比指标处理
          if (indicator.unit === '%') {
            if (value < 1 && value > 0) {
              value = (value * 100).toFixed(2)
            } else {
              value = value.toFixed(2)
            }
          } else if (typeof value === 'number') {
            value = value.toFixed(2)
          }
          
          childRow[columnKey] = value
          values.push({ key: columnKey, value: parseFloat(value) || 0 })
        })
        
        // 找出最优值
        if (indicator.optimize) {
          const bestSolutions = findBestSolutions(values, indicator.optimize)
          childRow.bestSolutions = bestSolutions
        }
        
        parentRow.children.push(childRow)
      })
      
      tableRows.push(parentRow)
    }
  })
  
  return tableRows
})

// 找出最优方案
const findBestSolutions = (values, optimize) => {
  if (values.length === 0) return []
  
  let bestValue
  if (optimize === 'max') {
    bestValue = Math.max(...values.map(v => v.value))
  } else {
    bestValue = Math.min(...values.filter(v => v.value > 0).map(v => v.value))
  }
  
  return values.filter(v => v.value === bestValue).map(v => v.key)
}


// 格式化数值 - 只显示数值，不显示单位
const formatValue = (value, unit) => {
  if (value === undefined || value === null || value === '') return '-'
  
  const numValue = parseFloat(value)
  if (isNaN(numValue)) return value
  
  // 如果是0值，显示0
  if (numValue === 0) return '0'
  
  // // 大数值格式化
  // if (unit === '万元' && numValue >= 10000) {
  //   return `${(numValue / 10000).toFixed(2)}亿`
  // }
  
  return value
}

// 获取对比结果数据
const getCompareResult = async (solutionIds) => {
  if (!solutionIds || solutionIds.length === 0) return
  
  loading.value = true
  try {
    const { code, data, msg } = await getEconomicCompareResult(solutionIds)
  if (code === 0) {
    solutions.value = data
      generateAnalysis()
      console.log('API返回数据:', data)
  } else {
    message.error(msg)
  }
  } catch (error) {
    message.error('获取对比数据失败')
    console.error('error', error)
  } finally {
    loading.value = false
  }
}

const generateAnalysis = () => {
  if (solutions.value.length === 0) return
  
  const analysis = []
  
  // 项目收益率最高
  let maxProfitRate = -1
  let maxProfitSolution = null
  solutions.value.forEach(solution => {
    const rate = solution.result?.resultTables?.financialIndicatorsSummary?.projectInvestmentFIRR_afterTax || 0
    if (rate > maxProfitRate) {
      maxProfitRate = rate
      maxProfitSolution = solution.solution?.name
    }
  })
  if (maxProfitSolution && maxProfitRate > 0) {
    // 处理百分比显示，如果是小数形式需要乘100
    const displayRate = maxProfitRate < 1 ? (maxProfitRate * 100).toFixed(2) : maxProfitRate.toFixed(2)
    analysis.push({
      label: '项目收益率最高',
      value: `${maxProfitSolution}（${displayRate}%）`
    })
  }
  
  // 总投资最少
  let minInvestment = Infinity
  let minInvestmentSolution = null
  solutions.value.forEach(solution => {
    const investment = solution.result?.resultTables?.financialIndicatorsSummary?.projectTotalInvestment || 0
    if (investment > 0 && investment < minInvestment) {
      minInvestment = investment
      minInvestmentSolution = solution.solution?.name
    }
  })
  if (minInvestmentSolution) {
    // 格式化投资金额显示
    const displayInvestment = minInvestment >= 10000 ? 
      `${(minInvestment / 10000).toFixed(2)}亿元` : 
      `${minInvestment.toFixed(2)}万元`
    analysis.push({
      label: '总投资最少',
      value: `${minInvestmentSolution}（${displayInvestment}）`
    })
  }
  
  // 静态回收期最短
  let minPayback = Infinity
  let minPaybackSolution = null
  solutions.value.forEach(solution => {
    const payback = solution.result?.resultTables?.financialIndicatorsSummary?.paybackPeriodAfterTax_static || 0
    if (payback > 0 && payback < minPayback) {
      minPayback = payback
      minPaybackSolution = solution.solution?.name
    }
  })
  if (minPaybackSolution) {
    analysis.push({
      label: '静态回收期最短',
      value: `${minPaybackSolution}（${minPayback.toFixed(2)}年）`
    })
  }
  
  // 平准化制氢成本LCOH最低
  let minLcoh = Infinity
  let minLcohSolution = null
  solutions.value.forEach(solution => {
    const lcoh = solution.result?.resultTables?.financialIndicatorsSummary?.lcoh || 0
    if (lcoh > 0 && lcoh < minLcoh) {
      minLcoh = lcoh
      minLcohSolution = solution.solution?.name
    }
  })
  if (minLcohSolution) {
    analysis.push({
      label: '平准化制氢成本LCOH最低',
      value: `${minLcohSolution}（${minLcoh.toFixed(2)}元/Nm³）`
    })
  }
  
  analysisResult.value = analysis
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 跳转到方案详情页
const goToDetail = (solutionData) => {
  if (!solutionData?.solution?.id) {
    message.error('方案数据不完整')
    return
  }
  
  const projectId = solutionData.solution.projectId || solutionData.projectId
  const solutionId = solutionData.solution.id
  
  if (!projectId) {
    message.error('缺少项目ID')
    return
  }
  
  router.push({
    name: 'economicDetail',
    params: {
      projectId: projectId,
      solutionId: solutionId
    }
  })
}

// 初始化数据
const initData = () => {
  const solutionIdsParam = route.query.solutionIds
  if (!solutionIdsParam) {
    message.error('缺少方案参数')
    return
  }
  
  const solutionIds = solutionIdsParam.split(',').map(id => parseInt(id)).filter(id => !isNaN(id))
  if (solutionIds.length === 0) {
    message.error('方案参数格式错误')
    return
  }
  
  getCompareResult(solutionIds)
}

onMounted(() => {
  initData()
})
</script>

<style lang="less" scoped>
@import '@/style/base.less';

.body_wrap {
  font-size: 12px;
  padding: 10px 20px;
  position: relative;
}

.p_wrap {
  .content_wrap {
    .p_title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      
      .btn_wrap {
        .btn_item {
          margin-left: 10px;
        }
      }
    }
    
    .title-wrapper {
      flex: 1;
    }
    
    .analysis_wrap {
      margin-top: 24px;
      padding: 16px 0;
      
      .analysis_title {
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 16px;
        color: #333;
        display: flex;
        align-items: center;
        gap: 8px;
        
        .title_icon {
          font-size: 18px;
        }
      }
      
      .analysis_content {
        .analysis_item {
          padding: 8px 0;
          margin-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            margin-bottom: 0;
            border-bottom: none;
          }
          
          .analysis_label {
            font-size: 14px;
            font-weight: 700;
            color: #333;
            display: inline-block;
            margin-right: 8px;
            width: 180px;
          }
          
          .analysis_value {
            font-size: 14px;
            color: #333;
            display: inline;
          }
        }
      }
    }
    
    .table_wrap {
      .loading-state {
        text-align: center;
        padding: 60px 20px;
        
        .loading-text {
          margin-top: 16px;
          color: #666;
          font-size: 14px;
        }
      }
      
      .compare_table {
        :deep(.ant-table-thead > tr > th) {
          background: #f0f0f0;
          font-weight: 600;
          text-align: center;
          border-right: 1px solid #d9d9d9;
          
          &:first-child {
            text-align: left;
            width: 200px;
          }
        }
        
        .solution-header {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          
          .detail-link-icon {
            color: #1890ff;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            
            &:hover {
              color: #40a9ff;
              transform: scale(1.1);
            }
          }
        }
        
        :deep(.ant-table-tbody > tr > td) {
          padding: 12px;
          border-right: 1px solid #f0f0f0;
          
          &:first-child {
            font-weight: 500;
            background-color: #fafafa;
            border-right: 1px solid #d9d9d9;
          }
        }
        
        // 树形表格样式
        :deep(.ant-table-row-expand-icon) {
          // color: #1890ff;
          
          &.ant-table-row-expand-icon-collapsed,
          &.ant-table-row-expand-icon-expanded {
            border: 1px solid #d9d9d9;
            background: #fff;
          }
        }
        
        .indicator-name {
          font-weight: 500;
          color: #333;
          
          &.parent-indicator {
            font-weight: 600;
            // color: #2caa89;
          }
          
          &.child-indicator {
            font-weight: 500;
            color: #4a5568;
          }
          
          &.single-indicator {
            font-weight: 600;
            color: #333;
          }
          
          .unit-text {
            font-weight: 400;
            color: #999;
            font-size: 12px;
            margin-left: 4px;
          }
        }
        
        .parent-cell {
          height: 20px; // 父级行数据列留空
        }
        
        .value-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          
          .best-icon {
            color: #ffa940;
            font-size: 16px;
          }
        }
        
        // 悬停效果
        :deep(.ant-table-tbody > tr:hover > td) {
          background-color: #e6f7ff !important;
        }
        
        // 空状态样式
        .empty-state {
          padding: 40px 20px;
          text-align: center;
          
          .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
          }
          
          .empty-text {
            color: #999;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
