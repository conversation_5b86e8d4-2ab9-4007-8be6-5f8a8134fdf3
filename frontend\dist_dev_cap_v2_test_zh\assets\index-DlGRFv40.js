import{k as le,g as ne,l as ie}from"./index-uvx8ZPMm.js";import{_ as ce,u as re,d as ue,r as p,o as de,f as v,b as m,c as h,g as o,t as n,h as r,w as u,i as b,v as N,n as w,p as j,m as M,F as z,l as F,E as pe,G as me,M as _e,L as ve,N as fe}from"./index-CtrMLNJH.js";import{p as G,i as be,c as ge,t as je,a as he}from"./util-fK6aekuJ.js";import{l as ke}from"./lodash-BEYymIPm.js";/* empty css                                                              */import{f as ye,g as H}from"./index-UGdhnffj.js";const we=C=>(pe("data-v-364b1a8c"),C=C(),me(),C),Ce={class:"body_wrap"},$e={class:"p_wrap"},Le=we(()=>o("div",{class:"title_wrap"},null,-1)),Se={class:"content_wrap",id:"content_wrap"},Pe={class:"part_wrap"},Ne={class:"p_title"},Me={class:"btn_wrap"},Re={class:"table_wrap"},Ie={key:0,class:"t_btn_wrap"},qe=["onClick"],ze=["onClick"],Te={key:0,class:"t_btn_wrap"},Ae=["onClick"],De=["onClick"],Be=["onClick"],Oe=["onClick"],Ue={__name:"index",setup(C){const{t:K}=re(),$=ue(),R=p(!1),L=p(!1),T=p(),A=p([]),f=p({}),D=p({}),I=p(!1),B=p(!1),O=p(0),i=p({operator:"",pageSize:10,pageNumber:1,searchWord:""}),J=e=>{const t=[],s=je();for(let a=0,d=s.length;a<d;a++)(e==null?void 0:e[a])!==0&&t.push({label:s[a],value:e==null?void 0:e[a]});return t},Q=e=>{const t=[],s=he();for(let a=0,d=s.length;a<d;a++)e!=null&&e[a]&&t.push({label:s[a]});return t},X=()=>{$.push({name:"createProject"})},U=(e,t)=>{console.log("ceateSolution:",e,t);const s={projectId:e.id};(t==null?void 0:t.id)!==void 0&&(s.solutionId=t.id),$.push({name:"createProject",query:s})},Y=e=>{L.value=!0,D.value=e,f.value=ke.cloneDeep(e)},Z=async()=>{const e=await T.value.validateFields();I.value=!0;const{code:t,msg:s}=await le({id:D.value.id,...e});I.value=!1,t===0?(N.success(K("projectList.editProjectModal.editSuccess")),V()):N.error(s),L.value=!1},x=(e,t)=>{$.push({name:"economicAnalysisCreate",query:{capProjectId:e.id,capSolutionId:t.id}})},S=async(e=!0)=>{var k;R.value=e;const t={pageSize:i.value.pageSize,pageNumber:i.value.pageNumber};i.value.searchWord&&(t.search=i.value.searchWord),(k=i.value.operator)!=null&&k.trim()&&(t.operator=i.value.operator);const{msg:s,data:a,code:d}=await ne(t);console.log("code:",d,a),R.value=!1,d===0&&(A.value=a.result,O.value=a.total)},ee=e=>{console.log("page:",e),i.value.pageNumber=e.current,i.value.pageSize=e.pageSize,S()},te=async(e,t)=>{console.log("delSolution",e,t),_e.confirm({title:`确认删除当前方案 ${t.name}？`,icon:ve(fe),async onOk(){const{code:s,msg:a}=await ie([t.id]);s===0?(N.success("删除成功"),S()):N.error(a)},onCancel(){console.log("Cancel")}})},V=async()=>{S()};return de(()=>{V(),setTimeout(()=>{B.value=!0},5e3)}),(e,t)=>{const s=v("a-button"),a=v("a-input-search"),d=v("a-tag"),k=v("a-table"),W=v("a-input"),q=v("a-form-item"),ae=v("a-textarea"),oe=v("a-form"),se=v("a-modal");return m(),h("div",Ce,[o("div",$e,[Le,o("div",Se,[o("div",Pe,[o("div",Ne,[o("div",null,n(e.$t("projectList.title")),1),o("div",Me,[r(s,{class:"btn_item",size2:"small",type:"primary",onClick:X},{default:u(()=>[w(n(e.$t("projectList.buttons.newProject")),1)]),_:1})])]),o("div",Re,[o("div",null,[r(a,{value:i.value.searchWord,"onUpdate:value":t[0]||(t[0]=l=>i.value.searchWord=l),placeholder:e.$t("projectList.search.placeholder"),style:{width:"230px","margin-bottom":"15px"},onSearch:S},null,8,["value","placeholder"])]),r(k,{borderd:"",class:"outer_table ant-table-striped","row-class-name":(l,_)=>_%2===1?"table-striped":null,loading:R.value,columns:b(ge)(),rowKey:"id","data-source":A.value,defaultExpandAllRows:B.value,onChange:ee,pagination:{pageSize:i.value.pageSize,total:O.value,hideOnSinglePage:!1,showTotal:l=>e.$t("projectList.pagination.total",{total:l})}},{bodyCell:u(({column:l,record:_})=>[l.key==="action"?(m(),h("div",Ie,[o("a",{class:"a_item",onClick:g=>U(_)},n(e.$t("projectList.buttons.newSolution")),9,qe),o("a",{class:"a_item",onClick:g=>Y(_)},n(e.$t("projectList.buttons.editProject")),9,ze)])):j("",!0),l.key==="solution"?(m(),M(k,{key:1,class:"inner_table",showHeader:!0,columns:b(be)(),"data-source":_.solutions,pagination:!1,size:"small",defaultExpandAllRows:!0},{bodyCell:u(({column:g,text:y,record:P})=>{var E;return[g.key==="action"?(m(),h("div",Te,[o("a",{class:"a_item",onClick:c=>b($).push({name:"projectDetail",query:{projectId:_.id,solutionId:P.id,type:"list"}})},n(e.$t("projectList.buttons.view")),9,Ae),o("a",{class:"a_item",onClick:c=>U(_,P)},n(e.$t("projectList.buttons.editSolution")),9,De),o("a",{class:"a_item",onClick:c=>x(_,P)},n(e.$t("projectList.buttons.economicAnalysis")),9,Be),o("a",{class:"a_item",onClick:c=>te(_,P)},n(e.$t("projectList.buttons.delSolution")),9,Oe)])):j("",!0),g.key==="createTime"?(m(),h(z,{key:1},[w(n(b(ye)(y)),1)],64)):j("",!0),g.key==="status"?(m(),M(d,{key:2,color:(E=b(H)(b(G)(),y))==null?void 0:E.color},{default:u(()=>{var c;return[w(n((c=b(H)(b(G)(),y))==null?void 0:c.label),1)]}),_:2},1032,["color"])):j("",!0),g.key==="topology"?(m(!0),h(z,{key:3},F(Q(y),c=>(m(),M(d,null,{default:u(()=>[w(n(c.label),1)]),_:2},1024))),256)):j("",!0),g.key==="targetExpr"?(m(!0),h(z,{key:4},F(J(y),c=>(m(),M(d,null,{default:u(()=>[w(n(`${c.label}: ${c.value*100}%`),1)]),_:2},1024))),256)):j("",!0)]}),_:2},1032,["columns","data-source"])):j("",!0)]),_:1},8,["row-class-name","loading","columns","data-source","defaultExpandAllRows","pagination"])])])])]),r(se,{open:L.value,"onUpdate:open":t[4]||(t[4]=l=>L.value=l),title:e.$t("projectList.editProjectModal.title"),onOk:Z,"confirm-loading":I.value},{default:u(()=>[o("div",null,[r(oe,{labelAlign:"left2",ref_key:"formRef",ref:T,model:f.value,name:"basic","label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:u(()=>[r(q,{label:e.$t("projectList.editProjectModal.projectName"),name:"name",rules:[{required:!0,message:e.$t("projectList.editProjectModal.projectNameRequired")}]},{default:u(()=>[r(W,{value:f.value.name,"onUpdate:value":t[1]||(t[1]=l=>f.value.name=l)},null,8,["value"])]),_:1},8,["label","rules"]),r(q,{label:e.$t("projectList.editProjectModal.customerName"),name:"customer",rules:[{required:!0,message:e.$t("projectList.editProjectModal.customerNameRequired")}]},{default:u(()=>[r(W,{value:f.value.customer,"onUpdate:value":t[2]||(t[2]=l=>f.value.customer=l)},null,8,["value"])]),_:1},8,["label","rules"]),r(q,{label:e.$t("projectList.editProjectModal.projectBackground"),name:"desc",rules:[{required:!0,message:e.$t("projectList.editProjectModal.projectBackgroundRequired")}]},{default:u(()=>[r(ae,{value:f.value.desc,"onUpdate:value":t[3]||(t[3]=l=>f.value.desc=l)},null,8,["value"])]),_:1},8,["label","rules"])]),_:1},8,["model"])])]),_:1},8,["open","title","confirm-loading"])])}}},Ke=ce(Ue,[["__scopeId","data-v-364b1a8c"]]);export{Ke as default};
