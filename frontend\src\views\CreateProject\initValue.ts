export const formDefaultVal = {
  // target0: 0.5,
  // target1: 0.5,
  // target2: 0,
  // target3: 0,
  // scene0: 0,
  // scene1: 0,
  // scene2: 0,
  // scene3: 0,
  // scene4: 0,
  // scene5: 0,

  //all:
  projectName: undefined, // 项目名称
  customer: undefined, //客户名称
  desc: undefined, //项目描述
  location: { address: '', lng: 0, lat: 0 }, // 项目位置
  h2Product: undefined, //年产氢量 kg
  cycle: 25, //项目周期 年
  loanRadio: 0.7, //贷款比例
  loanCycle: 15, //贷款周期
  loanRate: 0.031, //贷款利率
  solutionName: undefined, //方案名称
  topology: [1,0,0,0,1,0], //场景 [光伏,风电,电网,储能,制氢,储氢]
  targetExpr: [1, 0, 0, 0], //求解目标 [LCOH,投资成本最低,弃电率最低,产氢量最大]
  algorithm: 1, // 求解算法 1-快速求解

  pv_min_capacity: 0, //光伏容量下限 MW
  pv_max_capacity: undefined, //光伏容量上限 MW
  pv_dev_ids: undefined, // 光伏设备ID
  pv_damp_rate: undefined, // 光伏衰减率
  wind_min_capacity: 0, //风电容量下限 MW
  wind_max_capacity: undefined, //风电容量上限 MW

  grid_down_radio: 0.1, //下网比例
  grid_zone_id: undefined,  //电网地区
  grid_year: undefined, //年份
  grid_sale_price: 0.332, //上网电价
  grid_up_radio: 0.2, //上网比例

  es_dev_ids: [], //储能设备ID
  es_min_capacity: 0, //储能容量下限
  es_max_capacity: undefined, //储能容量上限
  es_policy: 1, //储能策略
  es_min_radio: 0.1, // 储能最低配置比例

  ele_dev_ids: [], //电解槽设备ID
  ele_min_capacity: 0, //电解槽容量下限
  ele_max_capacity: undefined, //电解槽容量上限
  ele_policy: 2, //电解槽群控策略
  water_price: 4.38, //用水价格 元/吨
  h2_water_consuming: 1.4, // 单位制氢耗水 L/Nm3

  hs_dev_ids: [], // 储罐设备ID
  hs_min_capacity: 0, //储罐容量下限 kg?
  hs_max_capacity: 0, //储罐容量上限 kg?
  max_increase_load_rate: 0.2, //最大升负荷速率
  max_down_load_rate: 0.2, //最大降负荷速率
  adjust_time: undefined, //负荷调整响应时间 分钟
  adjust_interval: undefined, //负荷调节间隔 分钟
  absorb_id: undefined, //消纳配置表ID
  supply_h2: undefined, // 每小时供氢量

  pv_forecast_zone_id: undefined, //光伏预测地区ID
  pv_forecast: undefined, //光伏出力预测
  wind_forecast_zone_id: undefined, //风电预测地区ID
  wind_forecast: undefined, //风电出力预测

  pv_epc: 2.8, //光伏EPC 元/W
  wind_epc: 3.2, //风电EPC 元/W
  es_epc: 0.7, //储能EPC 元/W
  h2_invest: 2, //制氢投资 元/W
  plant_invest: 1.5, //制氢厂房 元/W
  hs_invest: 2500, //储氢投资 元/Nm³

  discount_rate: 0.06, // 设备折现率
  pv_om_radio: 0, // 光伏运维比例 ？
  pv_om_cost: 0.05, // 光伏运营成本  元/W/年
  wind_om_radio: 0, // 风电运维比例
  wind_om_cost: 0.06, // 风电运营成本 元/W/年
  es_om_radio: 0, // 储能运维比例 ？
  es_om_cost: 0.04, // 储能运营成本 元/W/年
  h2_om_radio: 0.02, // 制氢运维比例 ？
  h2_om_cost: 0, // 制氢运营成本 元/W/年 ？
  hs_om_radio: 0.02, // 储氢运维比例
  hs_om_cost: 0, // 储氢运营成本 元/Nm³/年

  load_miss: 0, // 负荷缺失
  pv_line_loss_rate: 0,
  wind_line_loss_rate: 0,
}

export const formTestVal = {
  // target0: 0.5,
  // target1: 0.5,
  // target2: 0,
  // target3: 0,
  // scene0: 0,
  // scene1: 0,
  // scene2: 0,
  // scene3: 0,
  // scene4: 0,
  // scene5: 0,

  //all:
  projectName: undefined, // 项目名称
  customer: undefined, //客户名称
  desc: undefined, //项目描述
  h2Product: undefined, //年产氢量 kg
  cycle: 25, //项目周期 年
  loanRadio: 0.7, //贷款比例
  loanCycle: 15, //贷款周期
  loanRate: 0.031, //贷款利率
  solutionName: undefined, //方案名称
  topology: [1,1,1,1,1,1], //场景 [光伏,风电,电网,储能,制氢,储氢]
  targetExpr: [1, 0, 0, 0], //求解目标 [LCOH,投资成本最低,弃电率最低,产氢量最大]
  algorithm: 1, // 求解算法 1-快速求解

  pv_min_capacity: 0, //光伏容量下限 MW
  pv_max_capacity: undefined, //光伏容量上限 MW
  pv_dev_ids: undefined, // 光伏设备ID
  pv_damp_rate: undefined, // 光伏衰减率
  wind_min_capacity: 0, //风电容量下限 MW
  wind_max_capacity: undefined, //风电容量上限 MW

  grid_down_radio: 0.1, //下网比例
  grid_zone_id: undefined,  //电网地区
  grid_year: undefined, //年份
  grid_sale_price: 0.332, //上网电价
  grid_up_radio: 0.2, //上网比例

  es_dev_ids: [], //储能设备ID
  es_min_capacity: 0, //储能容量下限
  es_max_capacity: undefined, //储能容量上限
  es_policy: 1, //储能策略

  ele_dev_ids: [], //电解槽设备ID
  ele_min_capacity: 0, //电解槽容量下限
  ele_max_capacity: undefined, //电解槽容量上限
  ele_policy: 2, //电解槽群控策略
  water_price: 4.38, //用水价格 元/吨
  h2_water_consuming: 1.4, // 单位制氢耗水 L/Nm3

  hs_dev_ids: [], // 储罐设备ID
  // hs_min_capacity: 0, //储罐容量下限 kg?
  // hs_max_capacity: undefined, //储罐容量上限 kg?
  max_increase_load_rate: 0.2, //最大升负荷速率
  max_down_load_rate: 0.2, //最大降负荷速率
  adjust_time: undefined, //负荷调整响应时间 分钟
  adjust_interval: undefined, //负荷调节间隔 分钟
  absorb_id: 2, //消纳配置表ID

  pv_forecast_zone_id: undefined, //光伏预测地区ID
  pv_forecast: undefined, //光伏出力预测
  wind_forecast_zone_id: undefined, //风电预测地区ID
  wind_forecast: undefined, //风电出力预测

  pv_epc: 2.8, //光伏EPC 元/W
  wind_epc: 3.2, //风电EPC 元/W
  es_epc: 0.7, //储能EPC 元/W
  h2_invest: 2, //制氢投资 元/W
  plant_invest: 1.5, //制氢厂房 元/W
  hs_invest: 2500, //储氢投资 元/Nm³

  discount_rate: 0.06, // 设备折现率
  pv_om_radio: 0.02, // 光伏运维比例 ？
  pv_om_cost: 0.05, // 光伏运营成本  元/W/年
  wind_om_radio: 0.02, // 风电运维比例
  wind_om_cost: 0.06, // 风电运营成本 元/W/年
  es_om_radio: 0.02, // 储能运维比例 ？
  es_om_cost: 0.04, // 储能运营成本 元/W/年
  h2_om_radio: 0.02, // 制氢运维比例 ？
  h2_om_cost: 0, // 制氢运营成本 元/W/年 ？
  hs_om_radio: 0.02, // 储氢运维比例
  hs_om_cost: 0, // 储氢运营成本 元/Nm³/年
}
