<template>
  <div class="body_wrap">
    <div class="p_wrap">
      <div class="content_wrap" id="content_wrap">
        <div class="part_wrap">
          <div class="p_title">
            <div>经济分析</div>
            <div class="btn_wrap">
              <a-button class="btn_item" type="primary" @click="createNew">新建</a-button>
              <!-- <a-button class="btn_item" @click="importSolution">导入容量测算结果</a-button> -->
              <a-button class="btn_item" @click="compareSolution">方案对比</a-button>
            </div>
          </div>
          
          <!-- 选择状态显示区域 -->
          <!-- <div class="selection_info" v-if="selectedSolutionKeys.length > 0">
            <div class="selection_text">
              <a-tag :color="selectedSolutionKeys.length >= 5 ? 'orange' : 'blue'">
                已选择 {{ selectedSolutionKeys.length }}/5 个方案
              </a-tag>
            </div>
          </div>
           -->
          <div class="table_wrap">
            <a-table
              size="small"
              bordered
              class="economic_table ant-table-striped"
              :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
              :loading="loading"
              :columns="columns"
              :data-source="tableData"
              row-key="id"
              :pagination="{
                pageSize: searchForm.pageSize,
                current: searchForm.pageNumber,
                total: total,
                hideOnSinglePage: false,
                showTotal: (total, range) => `共 ${total} 个项目`,
                showSizeChanger: true,
                showQuickJumper: true,
                pageSizeOptions: ['5', '10', '20', '50']
              }"
              @change="handleTableChange"

            >
              <!-- <template #headerCell="{ column }">
                <template v-if="column.key === 'solutions'">
                  <div class="solution_column_header">
                    <table class="header_table">
                      <thead>
                        <tr>
                          <th class="header_cell selection_cell" style="width: 60px;display: inline-block;">选择</th>
                          <th class="header_cell" style="width: 18%;">方案名称</th>
                          <th class="header_cell" style="width: 22%;">场景类型</th>
                          <th class="header_cell" style="width: 11.5%;">项目收益率</th>
                          <th class="header_cell" style="width: 20%;">提交时间</th>
                          <th class="header_cell" style="width: 10%;">状态</th>
                          <th class="header_cell" style="width: 15%;">操作</th>
                        </tr>
                      </thead>
                    </table>
                  </div>
                </template>
                <template v-else>
                  {{ column.title }}
                </template>
              </template> -->
              
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'sequence'">
                  {{ getSequenceNumber(index) }}
                </template>
                <template v-if="column.key === 'solutionCount'">
                  {{ record.solutions.length }}
                </template>
                <template v-if="column.key === 'solutions'">
                  <!-- 方案数据表格，无表头 -->
                  <a-table
                    size="small"
                    class="inner_table"
                    :columns="solutionColumns"
                    :data-source="record.solutions"
                    :pagination="false"
                    :row-selection="getSolutionRowSelection()"
                    row-key="id"
                    :show-header="true"
                  >
                    <template #bodyCell="{ column, record: solutionRecord }">
                      <template v-if="column.key === 'status'">
                        <a-tag :color="getStatusColor(solutionRecord.status)">
                          {{ getStatusText(solutionRecord.status) }}
                        </a-tag>
                      </template>
                      <template v-if="column.key === 'profitRate'">
                        {{ solutionRecord.profitRate }}%
                      </template>
                      <template v-if="column.key === 'topology'">
                        <div style="text-align: left;">
                          <a-tag v-for="item in createSceneTagList(solutionRecord.topology)" :key="item.label">
                            {{ item.label }}
                          </a-tag>
                        </div>
                      </template>
                      <template v-if="column.key === 'action'">
                        <div class="t_btn_wrap">
                          <a href="javascript:void(0)" class="a_item" @click="handleView(solutionRecord)">查看</a>
                          <!-- <a href="javascript:void(0)" class="a_item" @click="handleDownload(solutionRecord)">下载</a> -->
                          <a href="javascript:void(0)" class="a_item" @click="handleModify(solutionRecord)">修改</a>
                          <a href="javascript:void(0)" class="a_item" @click="delSolution(solutionRecord)">删除</a>
                        </div>
                      </template>
                      <template v-if="column.key==='metrics'">
                        <div>{{ (solutionRecord?.metrics?.projectInvestmentFIRR_afterTax * 100).toFixed(2) }}%</div>
                      </template>
                      <template v-if="column.key==='lcoh'">
                        <div>{{ ((isNaN(solutionRecord?.metrics?.lcoh_ori) || !solutionRecord?.metrics?.lcoh_ori) ?  0 : solutionRecord?.metrics?.lcoh_ori).toFixed(3) }}</div>
                      </template>
                      <template v-if="column.key === 'status'">
                        <a-tag :color="getOptionByValue(projectStatus(), text)?.color">{{ getOptionByValue(projectStatus(), text)?.label  }}</a-tag>
                      </template>
                      <template v-if="column.key === 'createTime'">
                        {{ formatDate(solutionRecord.createTime) }}
                      </template>
                    </template>
                  </a-table>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { columns, solutionColumns, getStatusColor, getStatusText, createSceneTagList } from './util'
import { getEconomicProject } from '@/api/economic'
import { deleteSoutions } from '@/api/project'
import { formatDate, getOptionByValue } from '@/util'
import { projectStatus } from '@/views/ProjectList/util'
import { useRouter } from 'vue-router'

const [modal, contextHolder] = Modal.useModal();
const router = useRouter()
const loading = ref(false)
const tableData = ref([])
const selectedSolutionKeys = ref([])
const total = ref(0)
const totalSolutions = ref(0)
const searchForm = ref({
  pageSize: 10,
  pageNumber: 1
})

// 计算序号
const getSequenceNumber = (index) => {
  return (searchForm.value.pageNumber - 1) * searchForm.value.pageSize + index + 1
}

// 方案选择配置 - 统一管理所有方案的选择状态
const getSolutionRowSelection = () => ({
  selectedRowKeys: selectedSolutionKeys.value,
  onSelect: onSolutionSelect,
  hideSelectAll: true, // 隐藏表头全选框
  getCheckboxProps: (record) => {
    const isSelected = selectedSolutionKeys.value.includes(record.id)
    const isMaxSelected = selectedSolutionKeys.value.length >= 5
    return {
      disabled: !isSelected && isMaxSelected, // 如果未选择且已达到最大数量，则禁用
      name: record.solutionName,
    }
  },
})

// 方案选择变化处理 - 只使用 onSelect 来处理，移除 onChange 避免冲突
const onSolutionSelect = (record, selected, selectedRows) => {
  console.log('solution select:', record, selected, selectedRows)
  if (selected) {
    // 检查是否已达到最大选择数量
    if (selectedSolutionKeys.value.length >= 5) {
      message.warning('最多只能选择5个方案')
      // return false // 阻止选择
    }
    
    // 添加选中的方案
    if (!selectedSolutionKeys.value.includes(record.id)) {
      selectedSolutionKeys.value.push(record.id)
      console.log('added solution:', record.id, 'total selected:', selectedSolutionKeys.value)
    }
  } else {
    // 移除取消选中的方案
    selectedSolutionKeys.value = selectedSolutionKeys.value.filter(key => key !== record.id)
    console.log('removed solution:', record.id, 'total selected:', selectedSolutionKeys.value)
  }
}

// 表格变化处理（分页、排序、筛选）
const handleTableChange = (pagination, filters, sorter) => {
  console.log('Table change:', pagination, filters, sorter)
  searchForm.value.pageNumber = pagination.current
  searchForm.value.pageSize = pagination.pageSize
  getTableData()
}

// 获取表格数据
const getTableData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const { code, data: { total: t, result } } = await getEconomicProject(searchForm.value)
    
    tableData.value = result
    total.value = t
  } catch (error) {
    message.error('获取数据失败')
    console.log('error', error)
  } finally {
    loading.value = false
  }
}

// 按钮操作处理
const createNew = () => {
  router.push({
    name: 'economicAnalysisCreate',
  })
}

const delSolution = async (record) => {
  console.log('delSolution', record)
  Modal.confirm({
    title: `确认删除当前方案 ${record.name}？`,
    icon: h(ExclamationCircleOutlined),
    // content: h('div', { style: 'color:red;' }, 'Some descriptions'),
    async onOk() {
      const { code, msg } = await deleteSoutions([record.id])
        if (code === 0) {
          message.success('删除成功')
          getTableData()
        } else {
          message.error(msg)
        }
    },
    onCancel() {
      console.log('Cancel');
    },
  });
  
  
}

const compareSolution = () => {
  if (selectedSolutionKeys.value.length < 2) {
    message.warning('请至少选择2个方案进行对比')
    return
  }
  
  if (selectedSolutionKeys.value.length > 5) {
    message.warning('最多只能选择5个方案进行对比')
    return
  }
  router.push({
    path: '/economic/compare',
    query: {
      solutionIds: selectedSolutionKeys.value.join(',')
    }
  })
}

const handleView = (record) => {
  console.log('查看:', record)
  router.push({
    name: 'economicDetail',
    params: {
      projectId: record.projectId,
      solutionId: record.id
    }
  })
}

const handleModify = (record) => {
  console.log('修改:', record)
  router.push({   
    name: 'economicAnalysisCreate',
    query: {
      solutionId: record.id,
      projectId: record.projectId
    }
  })
}

// 初始化数据
const initData = () => {
  getTableData()
}

onMounted(() => {
  initData()
})
</script>

<style lang="less" scoped>
@import '@/style/base.less';

.body_wrap {
  font-size: 12px;
  padding: 10px 20px;
  position: relative;
}

.p_wrap {
  .content_wrap {
    .p_title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 20px;
      
      .btn_wrap {
        .btn_item {
          margin-left: 10px;
        }
      }
    }
    
    .selection_info {
      margin-bottom: 20px;
      
      .selection_text {
        margin-right: 10px;
      }
    }
    
    .table_wrap {
      overflow-x: auto; // 只在必要时显示水平滚动条
      
      .economic_table {
        // 确保主表自适应宽度
        width: 100%;
        min-width: 1000px; // 设置最小宽度，避免在小屏幕上压缩过度
        
        :deep(.ant-table-tbody) {
          .ant-table-row {
            &:hover {
              background-color: #e6f7ff;
            }
          }
        }
        
        // 主表列宽度控制
        :deep(.ant-table-thead > tr > th) {
          &:nth-child(1) { width: 130px; padding-left: 17px; } // 项目名称
          &:nth-child(2) { width: 150px; padding-left: 17px; } // 项目背景
          &:nth-child(3) { width: 100px; padding-left: 17px; } // 客户名称
          &:nth-child(4) { width: auto; } // 方案列表 - 自适应剩余宽度
        }
        
        // 主表体单元格样式
        :deep(.ant-table-tbody > tr > td) {
          &:nth-child(1) { padding-left: 17px; } // 项目名称
          &:nth-child(2) { padding-left: 17px; } // 项目背景
          &:nth-child(3) { padding-left: 17px; } // 客户名称
        }
        
          .inner_table {
            border-top: none;
            width: 100%;
            
            :deep(.ant-table) {
              border-top: none;
              table-layout: fixed; // 强制使用固定表格布局
              width: 100%;
              margin: 0; // 移除默认边距
            }
            
            :deep(.ant-table-wrapper) {
              width: 100%;
            }
          
          :deep(.ant-table-container) {
            border-top: none;
          }
          
          :deep(.ant-table-thead > tr > th) {
            // background: #f5f5f5;
            font-weight: 500;
            text-align: left;
            
            // 子表列宽度控制
            &:nth-child(1) { width: 32px; } // 选择框
            &:nth-child(2) { width: 140px; } // 方案名称
            &:nth-child(3) { width: 220px; } // 场景类型
            &:nth-child(4) { width: 130px; } // 项目收益率
            &:nth-child(5) { width: 160px; } // 提交时间
            &:nth-child(6) { width: 160px; } // 操作
          }
          
          :deep(.ant-table-tbody > tr:hover > td) {
            background-color: #f0f9ff;
          }
          
          // 确保子表列宽与表头对齐
          :deep(.ant-table-tbody > tr > td) {
            padding: 8px 12px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            
            &:nth-child(1) { 
              text-align: left;
              padding-left: 8px;
              width: 32px;
            } // 选择框
            &:nth-child(2) { 
              text-align: left; 
              width: 140px;
            } // 方案名称
            &:nth-child(3) { 
              text-align: left; 
              width: 220px;
            } // 场景类型
            &:nth-child(4) { 
              text-align: center; 
              width: 130px;
            } // 项目收益率
            &:nth-child(5) { 
              text-align: left; 
              width: 160px;
            } // 提交时间
            &:nth-child(6) { 
              text-align: left; 
              width: 160px;
            } // 操作
          }
          
          // 第一行数据与表头连接
          :deep(.ant-table-tbody > tr:first-child > td) {
            border-top: none;
          }
        }
        
        .solution_column_header {
          width: 100%;
          
          .header_table {
            width: 100%;
            border-collapse: collapse;
            
            .header_cell {
              padding: 8px 12px;
              font-size: 14px;
              font-weight: 500;
              color: rgba(0, 0, 0, 0.85);
              border-right: 1px solid #f0f0f0;
              text-align: center;
              background: transparent;
              border: none;
              
              &:first-child {
                text-align: left;
              }
              
              &:last-child {
                border-right: none;
              }
            }
            
            .selection_cell {
              width: 32px; // 选择框列宽度
            }
          }
        }
        
        .t_btn_wrap {
          .a_item {
            margin-right: 8px;
            font-size: 12px;
            color: #1890ff;
            text-decoration: none;
            
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
</style>
