<template>
  <div class="bg_wrap">
    <div v-if="longLogo">
      <img class="logo_img" src="@/assets/imgs/logo.png" />
    </div>
    <div class="login_window">
      <div class="title" v-if="longLogo">
        <img src="@/assets/imgs/login_logo.png" class="img_wrap"  />
      </div>
      <div class="title" v-else>{{ baseConfig.title }}</div>
      <a-form
        class="form_wrap"
        :model="formState"
        name="basic"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
        autocomplete="off"
        @finish="submit"
      >
        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入用户名!' }]"
        >
          <a-input v-model:value="formState.username" />
        </a-form-item>
        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码!' }]"
        >
          <a-input-password v-model:value="formState.password" />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 14 }">
          <a-button
            :loading="isLoading"
            style="width:100%" type="primary" html-type="submit"
          >
            登录
        </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { login } from '@/api/auth'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { baseConfig } from '@/config/baseConfig'
// import jsCookie from 'js-cookie' 

const isLoading = ref(false)
const longLogo = computed(() => {
  return import.meta.env.VITE_APP_ENV === 'zh'
})
const router = useRouter()
interface FormState {
  username: string;
  password: string;
}

const formState = reactive<FormState>({
  username: '',
  password: '',
})

const submit = async (values: FormState) => {
  isLoading.value = true
  const { code, msg } = await login(values)
  isLoading.value = false
  if (code === 0) {
    console.log('login success')

    router.push({ name: 'home' })
  } else {
    message.error(msg)
  }
};

</script>

<style lang="less" scoped>
@import '@/style/base.less';

.bg_wrap {
  width: 100vw;
  height: 100vh;
  background: url(@bgImgUrl) no-repeat 0 0/cover;
  .logo_img {
    margin: 15px 20px;
    height: 65px;
  }
}
.login_window {
  width: 38%;
  height: 48%;
  max-height: 400px;
  min-height: 350px;
  max-width: 500px;
  min-width: 400px;
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translate(0, -50%);
  // background: rgba(0, 0, 0, 0.25);
  background: @loginWindowColor;
  border-radius: 10px;
  overflow: hidden;
  padding: 20px 10px;
  margin-top: 2%;
  .title {
    color: #fff;
    width: 100%;
    text-align: center;
    font-size: 32px;
    margin-top: 10px;
    .img_wrap {
      height: 48px;
    }
  }
}
.form_wrap {
  width: 100%;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: 2%;
  transform: translate(-50%, -50%);
  :deep(.ant-form-item-required) {
    // color: #fff;
    color: @loginWordColor;

  }
}

</style>

