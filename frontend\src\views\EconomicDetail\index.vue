<template>
  <div class="economic-detail">
    <!-- 顶部标题和操作按钮 -->
    <div class="header-section">
      <div class="title-wrapper">
        <a-breadcrumb>
          <a-breadcrumb-item><a  @click="router.back()"> < 经济分析</a></a-breadcrumb-item>
          <a-breadcrumb-item>分析结果</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div class="header-actions">
        <a-button class="action-btn" @click="downloadReport" :loading="loading">下载报告</a-button>
        <a-button class="action-btn" @click="exportData" :loading="exportLoading">数据导出excel</a-button>
        <a-button type="primary" class="action-btn" @click="goToReportDetail">查看报告</a-button>
      </div>
    </div>

    <a-spin :spinning="loading" :tip="tips">
      <ProjectInfo :solution-info="solutionInfo" />

      <KeyIndicators :result-data="resultData" />

      <TabTables ref="tabTablesRef" :result-data="resultData" />

      <SensitivityAnalysis :sensitivity-data="sensitivityData" />
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getEconomicSolutionResult, getEconomicSolutionInfo } from '@/api/economic'
import ProjectInfo from './components/ProjectInfo.vue'
import KeyIndicators from './components/KeyIndicators.vue'
import TabTables from './components/TabTables.vue'
import SensitivityAnalysis from './components/SensitivityAnalysis.vue'
import { downloadFile, strToBase64 } from '@/util'
import { message } from 'ant-design-vue'
import * as XLSX from 'xlsx'

const router = useRouter()
const route = useRoute()
const resultData = ref(null)
const sensitivityData = ref(null)
const solutionInfo = ref({})
const loading = ref(false)
const tips = ref('数据加载中...')
const exportLoading = ref(false)
const tabTablesRef = ref(null)

const getResultData = async () => {
  try {
    loading.value = true
    const { code, data, msg} = await getEconomicSolutionResult({
      projectId: route.params.projectId,
      solutionId: route.params.solutionId
    })
    console.log('接口返回数据:', data)
    console.log('lcoh:', data.resultTables.financialIndicatorsSummary.lcoh)
    console.log('Excel原 lcoh_ori:', data.resultTables.financialIndicatorsSummary.lcoh_ori)
    console.log('lcoe:', data.resultTables.financialIndicatorsSummary.lcoe)
    console.log('Excel原 lcoe_ori:', data.resultTables.financialIndicatorsSummary.lcoe_ori)
    console.log('敏感性分析数据:', data.resultAnalysis)
    if (code === 0) {
      resultData.value = data.resultTables;
      // 设置敏感性分析数据
      sensitivityData.value = data.resultAnalysis || null;
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    resultData.value = null
  } finally {
    loading.value = false
  }
}

const getSolutionInfo = async () => {
  try {
    const { code, data, msg } = await getEconomicSolutionInfo({
      projectId: route.params.projectId,
      solutionId: route.params.solutionId
    })
    if (code === 0) {
      solutionInfo.value = data
    }
  } catch (error) {
    console.error('获取方案信息失败:', error)
  }
}
// 跳转到方案详情页
const goToReportDetail = () => {
  router.push({
    path: '/report-detail',
    query: {
      projectId: route.params.projectId,
      solutionId: route.params.solutionId
    }
  })
}

const downloadReport = async () => {
  loading.value = true
  tips.value = '报告生成中...'
  const str = await strToBase64(`${location.origin}/report-detail?projectId=${route.params.projectId}&solutionId=${route.params.solutionId}&type=1`)
  await downloadFile(`/api/v1/cecp/result/export?encodedUrl=${str}`, `${solutionInfo.value?.project?.name}.pdf`)

  setTimeout(() => {
    loading.value = false
  }, 6500);
}

// 数据导出功能
const exportData = async () => {
  if (!resultData.value) {
    message.warning('暂无数据可导出')
    return
  }

  try {
    exportLoading.value = true

    // 导出经济分析详情数据和敏感性分析数据
    await exportEconomicDetailWithSensitivityLocal()

    message.success('数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 导出包含敏感性分析的完整数据
const exportEconomicDetailWithSensitivityLocal = async () => {
  const workbook = XLSX.utils.book_new()

  // 1. 导出基础经济分析数据表格
  if (tabTablesRef.value && resultData.value) {
    const { tableConfigs, getTableColumns, getTableData } = tabTablesRef.value

    tableConfigs.forEach((config) => {
      const columns = getTableColumns(config)
      const data = getTableData(config)

      if (data && data.length > 0) {
        // 创建表头
        const headers = columns.map((col) => col.title)

        // 创建数据行
        const rows = data.map((row) => {
          return columns.map((col) => {
            const value = row[col.dataIndex]
            // 处理特殊值
            if (value === '--' || value === null || value === undefined) {
              return ''
            }
            return value
          })
        })

        // 合并表头和数据
        const sheetData = [headers, ...rows]

        // 创建工作表
        const worksheet = XLSX.utils.aoa_to_sheet(sheetData)

        // 设置列宽
        const colWidths = columns.map((col) => ({
          wch: Math.max(col.title.length, 15) // 最小宽度15字符
        }))
        worksheet['!cols'] = colWidths

        // 添加到工作簿
        XLSX.utils.book_append_sheet(workbook, worksheet, config.tab)
      }
    })
  }

  // 2. 导出敏感性分析数据
  if (sensitivityData.value && sensitivityData.value.length > 0) {
    // 指标名称映射
    const indicatorNameMap = {
      'investCost': '静态投资成本',
      'gridHydrogenElectricityPriceNoTax': '下网电价(不含税)',
      'gridElectricityPriceNoTax': '上网电价(不含税)',
      'baseLoanRate': '借款利率',
      'financingRatioBase': '贷款比例',
      'hydrogenPriceNoTax': '氢气售价(不含税)'
    }

    // 格式化数值函数
    const formatSignificantDigits = (value, indicatorKey) => {
      if (value === null || value === undefined || value === '-') {
        return '-'
      }

      let num = Number(value)
      if (isNaN(num)) {
        return '-'
      }

      // 检查是否为百分比单位的指标
      const percentageIndicators = ['baseLoanRate', 'financingRatioBase']
      const isPercentage = percentageIndicators.includes(indicatorKey)

      // 如果是百分比指标，将小数转换为百分比
      if (isPercentage) {
        num = num * 100
      }

      // 如果是0，直接返回
      if (num === 0) {
        return '0'
      }

      // 计算有效数字位数
      const absNum = Math.abs(num)
      let precision

      if (absNum >= 1000) {
        precision = 0
      } else if (absNum >= 100) {
        precision = 1
      } else if (absNum >= 10) {
        precision = 2
      } else if (absNum >= 1) {
        precision = 3
      } else {
        precision = 4
      }

      return num.toFixed(precision)
    }

    // 为每个指标创建一个工作表
    sensitivityData.value.forEach((indicator) => {
      const indicatorName = indicatorNameMap[indicator.key] || indicator.key

      if (indicator.value && indicator.result && indicator.value.length > 0) {
        // 获取单位信息
        const unitMap = {
          investCost: '万元',
          gridElectricityPriceNoTax: '元/kWh',
          gridHydrogenElectricityPriceNoTax: '元/kWh',
          baseLoanRate: '%',
          financingRatioBase: '%',
          hydrogenPriceNoTax: '元/kg'
        }
        const unit = unitMap[indicator.key] || ''

        // 创建表头
        const headers = [
          '变化幅度',
          `值(${unit})`,
          '项目投资财务内部收益率(税后)',
          '资本金财务内部收益率(税后)'
        ]

        // 创建数据行
        const rows = indicator.value.map((changeValue, index) => {
          const result = indicator.result[index] || {}

          // 格式化变化值
          let formattedChangeValue
          if (indicator.type === 0) {
            formattedChangeValue = `${(changeValue * 100).toFixed(1)}%`
          } else if (indicator.type === 1) {
            formattedChangeValue = changeValue >= 0 ? `+${changeValue}` : `${changeValue}`
          } else {
            formattedChangeValue = changeValue.toString()
          }

          return [
            formattedChangeValue,
            formatSignificantDigits(result.cur_value, indicator.key),
            `${((result.projectInvestmentFIRR_afterTax || 0) * 100).toFixed(2)}%`,
            `${((result.equityCapitalFIRR_afterTax || 0) * 100).toFixed(2)}%`
          ]
        })

        // 合并表头和数据
        const sheetData = [headers, ...rows]

        // 创建工作表
        const worksheet = XLSX.utils.aoa_to_sheet(sheetData)

        // 设置列宽
        worksheet['!cols'] = [
          { wch: 15 },
          { wch: 20 },
          { wch: 30 },
          { wch: 30 }
        ]

        // 添加到工作簿
        XLSX.utils.book_append_sheet(workbook, worksheet, `敏感性分析-${indicatorName}`)
      }
    })
  }

  // 3. 导出文件
  const fileName = `经济分析数据_${solutionInfo.value?.project?.name || '未命名项目'}_${new Date().toISOString().slice(0, 10)}.xlsx`
  XLSX.writeFile(workbook, fileName)
}

onMounted(async () => {
  await Promise.all([
    getResultData(),
    getSolutionInfo()
  ])
})
</script>

<style scoped lang="less">
@import '@/style/base.less';

.economic-detail {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 4px;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  height: 32px;
  padding: 0 15px;
  font-size: 14px;
}

.title-wrapper {
  flex: 1;
}
</style> 